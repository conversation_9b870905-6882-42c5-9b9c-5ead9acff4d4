<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytCellPhoneChangeAuditMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytCellPhoneChangeAudit">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="true_name" jdbcType="VARCHAR" property="trueName" />
    <result column="old_cell_phone" jdbcType="VARCHAR" property="oldCellPhone" />
    <result column="new_cell_phone" jdbcType="VARCHAR" property="newCellPhone" />
    <result column="hand_id_photo" jdbcType="VARCHAR" property="handIdPhoto" />
    <result column="hand_apply_for_photo" jdbcType="VARCHAR" property="handApplyForPhoto" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="reject_reason_code" jdbcType="INTEGER" property="rejectReasonCode" />
    <result column="reject_reason_msg" jdbcType="VARCHAR" property="rejectReasonMsg" />
    <result column="opt_id" jdbcType="BIGINT" property="optId" />
    <result column="opt_name" jdbcType="VARCHAR" property="optName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="selectList" resultMap="BaseResultMap">
    select  id,true_name,user_id,old_cell_phone,new_cell_phone,audit_status,opt_id,opt_name,create_time,modify_time
    from tyt_cell_phone_change_audit
    where 1=1
    <if test="oldCellPhone != null and oldCellPhone.length()>0">
      and old_cell_phone =#{oldCellPhone}
    </if>
    <if test="newCellPhone != null and newCellPhone.length()>0">
      and new_cell_phone =#{newCellPhone}
    </if>
    <if test="userId != null ">
      and user_id =#{userId}
    </if>
    <if test="auditStatus != null">
      and audit_status =#{auditStatus}
    </if>
    order by id desc
    </select>

  <update id="updateIdentityAuthMobile">
    UPDATE tyt_user_identity_auth set mobile=#{mobile,jdbcType=VARCHAR} where user_id=#{userId,jdbcType=BIGINT}
  </update>

  <update id="updateBlacklistUser">
    UPDATE blacklist_user SET cell_phone=#{newPhone,jdbcType=VARCHAR} WHERE user_id=#{userId,jdbcType=BIGINT} and cell_phone=#{oldPhone}
  </update>

  <update id="updateUserCellphoneInfo">
    update tyt_user SET cell_phone = #{cellPhone,jdbcType=VARCHAR},ticket = #{ticket,jdbcType=VARCHAR},mtime = now() where id = #{userId,jdbcType=BIGINT}
  </update>
</mapper>