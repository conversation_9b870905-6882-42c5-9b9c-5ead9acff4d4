<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.manager.mapper.base.TytTechRefundFinanceAuditMapper">
  <resultMap id="BaseResultMap" type="com.tyt.manager.entity.base.TytTechRefundFinanceAudit">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tech_refund_audit_id" jdbcType="BIGINT" property="techRefundAuditId" />
    <result column="ex_id" jdbcType="BIGINT" property="exId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_cell_phone" jdbcType="VARCHAR" property="userCellPhone" />
    <result column="user_apply_time" jdbcType="TIMESTAMP" property="userApplyTime" />
    <result column="ts_order_no" jdbcType="VARCHAR" property="tsOrderNo" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="dispatch_in_flow_no" jdbcType="VARCHAR" property="dispatchInFlowNo" />
    <result column="dispatch_out_flow_no" jdbcType="VARCHAR" property="dispatchOutFlowNo" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="audit_user_id" jdbcType="BIGINT" property="auditUserId" />
    <result column="audit_user_name" jdbcType="VARCHAR" property="auditUserName" />
    <result column="audit_cell_phone" jdbcType="VARCHAR" property="auditCellPhone" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="wallet_type" jdbcType="INTEGER" property="walletType" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="refund_err_msg" jdbcType="VARCHAR" property="refundErrMsg" />
    <result column="is_dispatch" jdbcType="INTEGER" property="isDispatch" />
    <result column="thirdparty_platform_type" jdbcType="INTEGER" property="thirdpartyPlatformType" />
    <result column="refund_tec_channel" jdbcType="INTEGER" property="refundTecChannel" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="getTechRefundFinanceAuditList" resultType="com.tyt.manager.entity.base.TytTechRefundFinanceAudit">
    select
    a.id id,
    a.tech_refund_audit_id techRefundAuditId,
    a.user_id userId,
    a.user_name userName,
    a.user_cell_phone userCellPhone,
    a.user_apply_time userApplyTime,
    a.ts_order_no tsOrderNo,
    a.refund_amount refundAmount,
    a.dispatch_in_flow_no dispatchInFlowNo,
    a.dispatch_out_flow_no dispatchOutFlowNo,
    a.audit_status auditStatus,
    a.audit_user_id auditUserId,
    a.audit_user_name auditUserName,
    a.audit_cell_phone auditCellPhone,
    a.audit_time auditTime,
    a.wallet_type walletType,
    a.refund_status refundStatus,
    a.refund_err_msg refundErrMsg,
    a.is_dispatch isDispatch,
    a.thirdparty_platform_type thirdpartyPlatformType,
    a.create_time createTime,
    a.modify_time modifyTime
    from tyt_tech_refund_finance_audit a
    where 1=1
    <if test="userId != null">
      and user_id = #{userId}
    </if>
    <if test="userName != null and userName != ''">
      and user_name = #{userName}
    </if>
    <if test="auditUserName != null and auditUserName != ''">
      and audit_user_name = #{auditUserName}
    </if>
    <if test="tsOrderNo != null and tsOrderNo != ''">
      and ts_order_no = #{tsOrderNo}
    </if>
    <if test="dispatchInFlowNo != null and dispatchInFlowNo != ''">
      and dispatch_in_flow_no = #{dispatchInFlowNo}
    </if>
    <if test="dispatchOutFlowNo != null and dispatchOutFlowNo != ''">
      and dispatch_out_flow_no = #{dispatchOutFlowNo}
    </if>
    <if test="auditStartTimeStr != null and auditStartTimeStr != ''">
      and audit_time &gt;= #{auditStartTimeStr}
    </if>
    <if test="auditEndTimeStr != null and auditEndTimeStr != ''">
      and audit_time &lt;= #{auditEndTimeStr}
    </if>
    <if test="auditStatus != null">
      and audit_status = #{auditStatus}
    </if>
    <if test="refundStatus != null">
      and refund_status = #{refundStatus}
    </if>
    order by a.modify_time desc, a.id desc
  </select>

  <select id="getTechRefundFinanceAuditListNew" resultType="com.tyt.techRefund.bean.TytTechRefundFinanceAuditVO">
    select
      trfa.id id,
      trfa.tech_refund_audit_id techRefundAuditId,
      trfa.user_id userId,
      trfa.user_name userName,
      trfa.user_cell_phone userCellPhone,
      trfa.user_apply_time userApplyTime,
      trfa.ts_order_no tsOrderNo,
      trfa.refund_amount refundAmount,
      trfa.dispatch_in_flow_no dispatchInFlowNo,
      trfa.dispatch_out_flow_no dispatchOutFlowNo,
      trfa.audit_status auditStatus,
      trfa.audit_user_id auditUserId,
      trfa.audit_user_name auditUserName,
      trfa.audit_cell_phone auditCellPhone,
      trfa.audit_time auditTime,
      trfa.wallet_type walletType,
      trfa.refund_status refundStatus,
      trfa.refund_err_msg refundErrMsg,
      trfa.is_dispatch isDispatch,
      trfa.thirdparty_platform_type thirdpartyPlatformType,
      trfa.create_time createTime,
      trfa.modify_time modifyTime ,       temp_apply.apply_detail_status applyDetailStatus ,
           temp_apply.status status
    from tyt_tech_refund_finance_audit trfa,
    tyt_transport_waybill_ex ex,
    tyt_transport_orders orders
    left join (select temp_apply_detail.order_id, temp_apply_detail.apply_id, apply.status status
                    ,temp_apply_detail.status apply_detail_status
    from tyt_invoice_apply apply
    join (SELECT apply_detail.order_id, apply_detail.apply_id
                ,apply_detail.status
    FROM tyt_invoice_apply_detail apply_detail
    JOIN (SELECT MAX(id) AS max_id, order_id
    FROM tyt_invoice_apply_detail
    where category = 2
    GROUP BY order_id) AS apply_detail_sub
    ON apply_detail.id = apply_detail_sub.max_id) temp_apply_detail
    on apply.id = temp_apply_detail.apply_id) temp_apply
    on temp_apply.order_id = orders.id
    where trfa.ex_id = ex.id
    and ex.order_id = orders.id
    <if test="userId != null">
      and trfa.user_id = #{userId}
    </if>
    <if test="userName != null and userName != ''">
      and trfa.user_name = #{userName}
    </if>
    <if test="auditUserName != null and auditUserName != ''">
      and trfa.audit_user_name = #{auditUserName}
    </if>
    <if test="tsOrderNo != null and tsOrderNo != ''">
      and trfa.ts_order_no = #{tsOrderNo}
    </if>
    <if test="dispatchInFlowNo != null and dispatchInFlowNo != ''">
      and trfa.dispatch_in_flow_no = #{dispatchInFlowNo}
    </if>
    <if test="dispatchOutFlowNo != null and dispatchOutFlowNo != ''">
      and trfa.dispatch_out_flow_no = #{dispatchOutFlowNo}
    </if>
    <if test="auditStartTimeStr != null and auditStartTimeStr != ''">
      and trfa.audit_time &gt;= #{auditStartTimeStr}
    </if>
    <if test="auditEndTimeStr != null and auditEndTimeStr != ''">
      and trfa.audit_time &lt;= #{auditEndTimeStr}
    </if>
    <if test="auditStatus != null">
      and trfa.audit_status = #{auditStatus}
    </if>
    <if test="refundStatus != null">
      and trfa.refund_status = #{refundStatus}
    </if>
    <if test="status != null">
      and temp_apply.status = #{status}
    </if>
    order by trfa.modify_time desc, trfa.id desc
  </select>


  <select id="getTechRefundFinanceAuditListNew2" resultType="com.tyt.techRefund.bean.TytTechRefundFinanceAuditVO">
    select
    trfa.id id,
    trfa.tech_refund_audit_id techRefundAuditId,
    trfa.user_id userId,
    trfa.user_name userName,
    trfa.user_cell_phone userCellPhone,
    trfa.user_apply_time userApplyTime,
    trfa.ts_order_no tsOrderNo,
    trfa.refund_amount refundAmount,
    trfa.dispatch_in_flow_no dispatchInFlowNo,
    trfa.dispatch_out_flow_no dispatchOutFlowNo,
    trfa.audit_status auditStatus,
    trfa.audit_user_id auditUserId,
    trfa.audit_user_name auditUserName,
    trfa.audit_cell_phone auditCellPhone,
    trfa.audit_time auditTime,
    trfa.wallet_type walletType,
    trfa.refund_status refundStatus,
    trfa.refund_err_msg refundErrMsg,
    trfa.is_dispatch isDispatch,
    trfa.thirdparty_platform_type thirdpartyPlatformType,
    trfa.create_time createTime,
    trfa.modify_time modifyTime ,
    trfa.refund_tec_channel refundTecChannel ,
    temp_apply.apply_detail_status applyDetailStatus ,
    temp_apply.status status
    FROM
    (
    (
    SELECT
    ttrfa.*, CASE WHEN ttrfa.order_id IS NOT NULL THEN ttrfa.order_id ELSE ex.order_id END AS orderId
    FROM tyt_tech_refund_finance_audit ttrfa LEFT JOIN tyt_transport_waybill_ex ex ON ttrfa.ex_id = ex.id
    ) trfa
    LEFT JOIN (
    select temp_apply_detail.order_id, temp_apply_detail.apply_id, apply.status status
    ,temp_apply_detail.status apply_detail_status
    from tyt_invoice_apply apply
    join (SELECT apply_detail.order_id, apply_detail.apply_id
    ,apply_detail.status
    FROM tyt_invoice_apply_detail apply_detail
    JOIN (SELECT MAX(id) AS max_id, order_id
    FROM tyt_invoice_apply_detail
    where category = 2
    GROUP BY order_id) AS apply_detail_sub
    ON apply_detail.id = apply_detail_sub.max_id) temp_apply_detail
    on apply.id = temp_apply_detail.apply_id) temp_apply ON temp_apply.order_id = trfa.orderId
    )
    where trfa.id IS NOT NULL
    <if test="userId != null">
      and trfa.user_id = #{userId}
    </if>
    <if test="userName != null and userName != ''">
      and trfa.user_name = #{userName}
    </if>
    <if test="auditUserName != null and auditUserName != ''">
      and trfa.audit_user_name = #{auditUserName}
    </if>
    <if test="tsOrderNo != null and tsOrderNo != ''">
      and trfa.ts_order_no = #{tsOrderNo}
    </if>
    <if test="dispatchInFlowNo != null and dispatchInFlowNo != ''">
      and trfa.dispatch_in_flow_no = #{dispatchInFlowNo}
    </if>
    <if test="dispatchOutFlowNo != null and dispatchOutFlowNo != ''">
      and trfa.dispatch_out_flow_no = #{dispatchOutFlowNo}
    </if>
    <if test="auditStartTimeStr != null and auditStartTimeStr != ''">
      and trfa.audit_time &gt;= #{auditStartTimeStr}
    </if>
    <if test="auditEndTimeStr != null and auditEndTimeStr != ''">
      and trfa.audit_time &lt;= #{auditEndTimeStr}
    </if>
    <if test="auditStatus != null">
      and trfa.audit_status = #{auditStatus}
    </if>
    <if test="refundStatus != null">
      and trfa.refund_status = #{refundStatus}
    </if>
    <if test="status != null">
      and temp_apply.status = #{status}
    </if>
    order by trfa.modify_time desc, trfa.id desc
  </select>


  <select id="getRefundTecVoucherByAuditId" resultType="com.tyt.web.back.internal.bean.RefundTecVoucherBean">
    select
    apply_reason as applyReason,
    apply_reason_remark as applyReasonRemark,
    picture_vouchers as pictureVouchers
    from tyt_transport_refund_tec_voucher where audit_id=#{id}
  </select>

</mapper>