package com.tyt.manager.mapper.base;

import com.tyt.manager.commons.tools.CustomBaseMapper;
import com.tyt.manager.entity.base.TytCellPhoneChangeAudit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TytCellPhoneChangeAuditMapper extends CustomBaseMapper<TytCellPhoneChangeAudit> {
    /**
     * @description 获取审核列表数据
     * <AUTHOR>
     * @date 2023/9/6 16:41
     * @version 1.0
     * @param
     * @return java.util.List<com.tyt.manager.entity.base.TytCellPhoneChangeAudit>
     */
    List<TytCellPhoneChangeAudit> selectList(@Param("oldCellPhone") String oldCellPhone,
                                             @Param("newCellPhone") String newCellPhone,
                                             @Param("userId") Long userId,
                                             @Param("auditStatus") Integer auditStatus);

    void updateIdentityAuthMobile(@Param("userId") Long userId, @Param("mobile") String mobile);

    void updateBlacklistUser(@Param("userId") Long userId, @Param("newPhone") String newPhone, @Param("oldPhone") String oldPhone);

    void updateUserCellphoneInfo(@Param("userId") Long userId,@Param("cellPhone") String cellPhone,@Param("ticket") String ticket);
}