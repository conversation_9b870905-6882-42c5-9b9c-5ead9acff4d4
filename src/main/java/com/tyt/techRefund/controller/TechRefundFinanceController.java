package com.tyt.techRefund.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.tyt.enums.InfofeeStatusEnum;
import com.tyt.equipment.bean.TransportWaybillExQueryBean;
import com.tyt.equipment.service.IPayExceptionService;
import com.tyt.equipment.service.TytTransportWaybillExService;
import com.tyt.exrepay.bean.ExRepayResultListResp;
import com.tyt.infofee.service.TransportOrdersService;
import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytTechRefundAudit;
import com.tyt.manager.entity.base.TytTechRefundFinanceAudit;
import com.tyt.manager.entity.base.TytTransportTechnicalOrder;
import com.tyt.manager.mapper.base.TytTransportTechnicalOrderMapper;
import com.tyt.model.*;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.service.invoice.enums.InvoiceApplyListStatusEnum;
import com.tyt.techRefund.bean.BatchTecAuditReq;
import com.tyt.techRefund.bean.BatchTecRefundResp;
import com.tyt.techRefund.bean.BatchTechRefundResp;
import com.tyt.techRefund.bean.TytTechRefundFinanceAuditVO;
import com.tyt.techRefund.enums.FinanceAuditStatusEnum;
import com.tyt.techRefund.enums.RefundTecChannelEnum;
import com.tyt.techRefund.service.TechRefundService;
import com.tyt.util.Constant;
import com.tyt.util.RedisLockUtils;
import com.tyt.web.base.BaseController;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 完单退技术服务费财务审核接口
 *
 * <AUTHOR>
 * @since 2024/06/22 16:41
 */
@RestController
@RequestMapping("/tech/refund/finance")
public class TechRefundFinanceController extends BaseController {

    @Autowired
    private TechRefundService techRefundService;

    @Autowired
    private IPayExceptionService payExceptionService;

    @Autowired
    private TytTransportTechnicalOrderMapper transportTechnicalOrderMapper;

    @Autowired
    private TransportOrdersService transportOrdersService;
    
    @Autowired
    private TytTransportWaybillExService transportWaybillExService;

    /**
     * 完单退还技术服务费，财务审核请求锁
     */
    public static final String REDIS_TECH_REFUND_FINANCE_AUDIT_LOCK_KEY = "tech:refund:finance:audit:";

    /**
     * 完单退技术服务费财务审核列表
     *
     * @param page              页码
     * @param size              每页条数
     * @param userId            用户id
     * @param userName          用户名
     * @param auditUserName     审核人
     * @param tsOrderNo         订单号
     * @param dispatchInFlowNo  代调钱包收款流水号
     * @param dispatchOutFlowNo 代调钱包出款流水号
     * @param auditStartTime    审核开始时间
     * @param auditEndTime      审核结束时间
     * @param auditStatus       财务审批状态 0.待审批 1.审批通过 2.审批拒绝
     * @param refundStatus      退款状态 1.退款中 2.退款成功 3.退款失败
     * @param status      开票状态 1.已提交 2.已开票 3.废弃 6:已红冲
     * @return com.tyt.model.ResultMsgBean
     */
    @RequestMapping("/list")
    public ResultMsgBean list(
            @RequestParam(value = "page", required = true) Integer page,
            @RequestParam(value = "size", required = true) Integer size,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "auditUserName", required = false) String auditUserName,
            @RequestParam(value = "tsOrderNo", required = false) String tsOrderNo,
            @RequestParam(value = "dispatchInFlowNo", required = false) String dispatchInFlowNo,
            @RequestParam(value = "dispatchOutFlowNo", required = false) String dispatchOutFlowNo,
            @RequestParam(value = "auditStartTime", required = false) Date auditStartTime,
            @RequestParam(value = "auditEndTime", required = false) Date auditEndTime,
            @RequestParam(value = "auditStatus", required = false) Integer auditStatus,
            @RequestParam(value = "refundStatus", required = false) Integer refundStatus,
            @RequestParam(value = "status", required = false) Integer status,
            HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                rm.setMsg("未登录");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            //查询完单退还技术服务费财务审核列表
            List<TytTechRefundFinanceAuditVO> techRefundFinanceAuditList = techRefundService.getTechRefundFinanceAuditListNew(page, size, userId, userName, auditUserName,
                    tsOrderNo, dispatchInFlowNo, dispatchOutFlowNo,
                    auditStartTime, auditEndTime, auditStatus, refundStatus,status);

            //判断是否有红冲的
            if(!CollectionUtil.isEmpty(techRefundFinanceAuditList)){
                for (TytTechRefundFinanceAuditVO tytTechRefundFinanceAuditVO : techRefundFinanceAuditList) {
                    if(tytTechRefundFinanceAuditVO.getApplyDetailStatus() != null &&
                            InvoiceApplyListStatusEnum.RED_CHONG.getStatus().equals(tytTechRefundFinanceAuditVO.getApplyDetailStatus())){
                        tytTechRefundFinanceAuditVO.setStatus(InvoiceApplyListStatusEnum.RED_CHONG.getStatus());
                    }
                }
            }

            PageInfo<TytTechRefundFinanceAuditVO> pageInfo = new PageInfo<>(techRefundFinanceAuditList);

            Map<String, String> parseRequestParams = this.parseRequestParams(request);
            List<TytJurisdiction> menus = (List<TytJurisdiction>) request.getSession().getAttribute("menus" + curUser.getId() + "_" + parseRequestParams.get("menuId"));

            HashMap<Object, Object> map = new HashMap<Object, Object>();
            map.put("pageInfo", pageInfo);
            map.put("menus", menus);
            rm.setData(map);
        } catch (Exception e) {
            logger.error("服务器异常", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        }
        return rm;
    }


    /**
     * 完单退技术服务费财务审核列表导出
     *
     * @param userId            用户id
     * @param userName          用户名
     * @param auditUserName     审核人
     * @param tsOrderNo         订单号
     * @param dispatchInFlowNo  代调钱包收款流水号
     * @param dispatchOutFlowNo 代调钱包出款流水号
     * @param auditStartTime    审核开始时间
     * @param auditEndTime      审核结束时间
     * @param auditStatus       财务审批状态 0.待审批 1.审批通过 2.审批拒绝
     * @param refundStatus      退款状态 1.退款中 2.退款成功 3.退款失败
     */
    @RequestMapping("/export")
    public void export(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "userName", required = false) String userName,
            @RequestParam(value = "auditUserName", required = false) String auditUserName,
            @RequestParam(value = "tsOrderNo", required = false) String tsOrderNo,
            @RequestParam(value = "dispatchInFlowNo", required = false) String dispatchInFlowNo,
            @RequestParam(value = "dispatchOutFlowNo", required = false) String dispatchOutFlowNo,
            @RequestParam(value = "auditStartTime", required = false) Date auditStartTime,
            @RequestParam(value = "auditEndTime", required = false) Date auditEndTime,
            @RequestParam(value = "auditStatus", required = false) Integer auditStatus,
            @RequestParam(value = "refundStatus", required = false) Integer refundStatus,
            HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                return;
            }
            //导出完单退还技术服务费财务审核列表
            techRefundService.exportTechRefundFinanceAuditList(userId, userName, auditUserName,
                    tsOrderNo, dispatchInFlowNo, dispatchOutFlowNo,
                    auditStartTime, auditEndTime, auditStatus, refundStatus, response);
        } catch (Exception e) {
            logger.error("服务器异常", e);
        }
    }


    /**
     * 完单退还技术服务费财务审批
     *
     * <AUTHOR>
     * @param id 审核id
     * @param auditStatus 审批状态  1.审批通过 2.审批拒绝
     * @param request http请求对象
     * @return ResultMsgBean
     */
    @RequestMapping("/audit")
    public ResultMsgBean audit(@RequestParam(value = "id", required = true) Long id,
                               @RequestParam(value = "auditStatus", required = true) Integer auditStatus,
                               HttpServletRequest request) {
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "更新成功");
        try {
            if (RedisLockUtils.acquire(REDIS_TECH_REFUND_FINANCE_AUDIT_LOCK_KEY + id, 60)) {
                // 获得当前用户的身份
                EmployeeQueryBean curUser = getCurrentUser(request);
                if (curUser == null) {
                    rm.setMsg("未登录");
                    rm.setCode(ReturnCodeConstant.ERROR);
                    return rm;
                }
                //0.根据完单退还技术服务费财务审核id查询财务审核信息
                TytTechRefundFinanceAudit techRefundFinanceAudit = techRefundService.getTechRefundFinanceAuditById(id);

                //1.前置校验逻辑(订金状态为完单、投诉单、且未处理过技术服务费)
                if(FinanceAuditStatusEnum.AUDIT_PASS.getCode() == auditStatus){
                    ResultMsgBean errorReturnMsg = techRefundFinanceAuditCheck(techRefundFinanceAudit, rm);
                    if (Objects.nonNull(errorReturnMsg)) {
                        return errorReturnMsg;
                    }
                }

                //2.更新退还技术服务费审核状态
                int result = techRefundService.updateFinanceAuditStatus(techRefundFinanceAudit, auditStatus, curUser);
                if(result <= 0){
                    rm.setMsg("更新退还技术服务费财务审核状态失败");
                    rm.setCode(ReturnCodeConstant.ERROR);
                    return rm;
                }
                //3.调用支付中心代调接口进行技术服务费代付操作(幂等性 失败不重复处理)
                if(result == 1 && FinanceAuditStatusEnum.AUDIT_PASS.getCode() == auditStatus){
                    ResultMsgBean resultMsgBean = techRefundService.sendCashBackRequest(techRefundFinanceAudit, request);
                    //4.根据代付返回信息更新技术服务费退款状态信息
                    techRefundService.updateTechRefundStatusByCashBackReturn(techRefundFinanceAudit, resultMsgBean);
                    //5.退还技术服务费后发送音转文mq
                    techRefundService.addSendASRTaskMqMessage(techRefundFinanceAudit);
                }
            }else{
                rm.setCode(ResultMsgBean.ERROR);
                rm.setMsg("完单退还技术服务费财务审批处理中,请稍等!!");
            }
            rm.setCode(ResultMsgBean.OK);
            rm.setMsg("完单退还技术服务费财务审批处理成功");
        } catch (Exception e) {
            logger.error("完单退还技术服务费财务审批异常，异常信息为：", e);
            rm.setCode(ReturnCodeConstant.ERROR);
            rm.setMsg("服务器错误");
        } finally {
            RedisLockUtils.release(REDIS_TECH_REFUND_FINANCE_AUDIT_LOCK_KEY + id);
        }
        return rm;
    }

    /**
     * 前置校验逻辑(订金状态为完单、投诉单、且未处理过技术服务费)
     *
     * <AUTHOR>
     * @param techRefundFinanceAudit 完单退还技术服务费财务审核对象
     * @param rm 返回结果信息
     * @return ResultMsgBean
     */
    private ResultMsgBean techRefundFinanceAuditCheck(TytTechRefundFinanceAudit techRefundFinanceAudit, ResultMsgBean rm) {
        //1.校验财务审核信息和财务审核状态
        if(Objects.isNull(techRefundFinanceAudit)){
            rm.setMsg("完单退还技术服务费财务审核信息不存在");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        if(techRefundFinanceAudit.getAuditStatus() != null
        && techRefundFinanceAudit.getAuditStatus() != FinanceAuditStatusEnum.AUDIT_IN.getCode()){
            rm.setMsg("完单退还技术服务费财务已审核完成，不允许重复处理");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        //代调、抽佣钱包收款流水号
        if(StringUtils.isBlank(techRefundFinanceAudit.getDispatchInFlowNo())){
            rm.setMsg("未查询到对应钱包的收款流水记录，不允许发起技术服务费退款操作");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        //钱包类型是否存在
        if(techRefundFinanceAudit.getWalletType() == null || techRefundFinanceAudit.getWalletType() == 0){
            rm.setMsg("钱包类型不存在或未知，不允许发起技术服务费退款操作");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        Integer refundTecChannel = techRefundFinanceAudit.getRefundTecChannel();
        //按照入口不同进行不同校验
        if(Objects.nonNull(refundTecChannel) && RefundTecChannelEnum.ORDER_FINISH.getCode() == refundTecChannel){
            //【完单后申请退还入口】校验技术服务费是否已退还
             return checkOrderFinishApply(techRefundFinanceAudit,rm);
        }else {
            //【完单后投诉入口】校验技术服务费是否已退还
            //2.校验运单信息是否存在
            Map<String, String> reqMap = new HashMap<>();
            Long exId = techRefundFinanceAudit.getExId();
            reqMap.put("id", String.valueOf(exId));
            TransportWaybillExQueryBean bean = payExceptionService.getExceptionOne(reqMap);
            if (Objects.isNull(bean)) {
                rm.setMsg("投诉订单信息不存在，不允许操作完单退还技术服务费");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            TytTransportOrders transportOrders = transportOrdersService.getById(bean.getOrderId());
            if (Objects.isNull(transportOrders)) {
                rm.setMsg("运单信息不存在，不允许操作完单退还技术服务费");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            //3. 校验是否为投诉订单
            if (bean.getOrderType() == null || bean.getOrderType() != 1) {
                rm.setMsg("该笔订单不是投诉订单，不允许操作完单退还技术服务费");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            //4. 校验订单是否完单
            if (bean.getCostStatus() != InfofeeStatusEnum.已退款.getId()
                    && bean.getCostStatus() != InfofeeStatusEnum.已打款.getId()
                    && bean.getCostStatus() != InfofeeStatusEnum.自动收款.getId()
                    && bean.getCostStatus() != InfofeeStatusEnum.异常完成.getId()) {
                rm.setMsg("该笔订单未完单，不允许操作完单退还技术服务费");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            //5. 校验订单是否处理过技术服务费
            //5.1 判断技术服务费支付金额是否大于0
            Long technicalServiceFee = bean.getTechnicalServiceFee();
            if (technicalServiceFee == null || technicalServiceFee <= 0L) {
                rm.setMsg("技术服务费未支付，不允许操作完单退还技术服务费");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            //5.2. 技术服务费是否已退款
            String technicalServiceNo = bean.getTechnicalServiceNo();
            if (StringUtils.isNotBlank(technicalServiceNo)) {
                TytTransportTechnicalOrder transportTechnicalOrder = transportTechnicalOrderMapper.selectByTechnicalServiceNo(technicalServiceNo);
                if (Objects.nonNull(transportTechnicalOrder)) {
                    Long dispatchRefundAmount = transportTechnicalOrder.getRefundAmount();
                    if (dispatchRefundAmount != null && dispatchRefundAmount > 0L) {
                        rm.setMsg("技术服务费已退款，不允许操作完单退还技术服务费");
                        rm.setCode(ReturnCodeConstant.ERROR);
                        return rm;
                    }
                }
            }
            //5.3. 技术服务费是否异常上报处理完成
            Long exRefundAmount = bean.getCarServiceAmount();
            Long platformServiceAmount = bean.getPlatformServiceAmount();
            if ((exRefundAmount != null && exRefundAmount > 0L)
                    || (platformServiceAmount != null && platformServiceAmount > 0L)) {
                rm.setMsg("技术服务费已异常上报处理完成，不允许操作完单退还技术服务费");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
            //5.4. 技术服务费是否已完单后退还(之前是否处理过:完单后投诉入口进来的)
            TytTechRefundAudit tytTechRefundAudit = techRefundService.getTechRefundAuditByIdAndExIdAndStatus(techRefundFinanceAudit.getTechRefundAuditId(), techRefundFinanceAudit.getExId());
            if (Objects.nonNull(tytTechRefundAudit)) {
                BigDecimal techRefundAuditAmount = tytTechRefundAudit.getRefundAmount();
                if (techRefundAuditAmount != null && techRefundAuditAmount.compareTo(BigDecimal.ZERO) > 0) {
                    rm.setMsg("【投诉申请】完单退还技术服务费审核记录已存在，不允许重复处理操作");
                    rm.setCode(ReturnCodeConstant.ERROR);
                    return rm;
                }
            }
            //5.5. 技术服务费是否已完单后退还(之前是否处理过:完单后申请退还入口进来的)
            TytTechRefundAudit tytTechRefundAuditOrders = techRefundService.getTechRefundAuditByOrderIdAndId(techRefundFinanceAudit.getTechRefundAuditId(),transportOrders.getId());
            if(Objects.nonNull(tytTechRefundAuditOrders)){
                rm.setMsg("【完单申请】完单退还技术服务费审核记录已存在，不允许重复处理操作");
                rm.setCode(ReturnCodeConstant.ERROR);
                return rm;
            }
        }
        return null;
    }


    private ResultMsgBean checkOrderFinishApply(TytTechRefundFinanceAudit techRefundFinanceAudit,ResultMsgBean rm){
        TytTransportOrders bean = transportOrdersService.getById(techRefundFinanceAudit.getOrderId());
        //3.校验运单信息是否存在
        if (Objects.isNull(bean)) {
            rm.setMsg("运单信息不存在，不允许操作完单退还技术服务费");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        //4. 校验订单是否完单
        if (bean.getCostStatus() != InfofeeStatusEnum.已退款.getId()
                && bean.getCostStatus() != InfofeeStatusEnum.已打款.getId()
                && bean.getCostStatus() != InfofeeStatusEnum.自动收款.getId()
                && bean.getCostStatus() != InfofeeStatusEnum.异常完成.getId()) {
            rm.setMsg("该笔订单未完单，不允许操作完单退还技术服务费");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        //5. 校验订单是否处理过技术服务费
        //5.1 判断技术服务费支付金额是否大于0
        Long technicalServiceFee = bean.getTecServiceFee();
        if (technicalServiceFee == null || technicalServiceFee <= 0L) {
            rm.setMsg("技术服务费未支付，不允许操作完单退还技术服务费");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        //5.2. 技术服务费是否已退款
        String technicalServiceNo = bean.getTechnicalServiceNo();
        if (StringUtils.isNotBlank(technicalServiceNo)) {
            TytTransportTechnicalOrder transportTechnicalOrder = transportTechnicalOrderMapper.selectByTechnicalServiceNo(technicalServiceNo);
            if (Objects.nonNull(transportTechnicalOrder)) {
                Long dispatchRefundAmount = transportTechnicalOrder.getRefundAmount();
                if (dispatchRefundAmount != null && dispatchRefundAmount > 0L) {
                    rm.setMsg("技术服务费已退款，不允许操作完单退还技术服务费");
                    rm.setCode(ReturnCodeConstant.ERROR);
                    return rm;
                }
            }
        }
        //5.3. 技术服务费是否异常上报处理完成
        List<TytTransportWaybillEx> transportWaybillExList = transportWaybillExService.getTransportWaybillExByOrderId(techRefundFinanceAudit.getOrderId(), 1);
        logger.info("transportWaybillExService getTransportWaybillExByOrderId orderId:{} transportWaybillExs size:{}",techRefundFinanceAudit.getOrderId(),transportWaybillExList.size());
        if(CollectionUtils.isNotEmpty(transportWaybillExList)){
           for (TytTransportWaybillEx waybillEx : transportWaybillExList) {
               Long exRefundAmount = waybillEx.getCarServiceAmount();
               Long platformServiceAmount = waybillEx.getPlatformServiceAmount();
               if ("2".equals(waybillEx.getExStatus())&&((exRefundAmount != null && exRefundAmount > 0L)
                       || (platformServiceAmount != null && platformServiceAmount > 0L))) {
                   rm.setMsg("技术服务费已异常上报处理完成，不允许操作完单退还技术服务费");
                   rm.setCode(ReturnCodeConstant.ERROR);
                   return rm;
               }
           }
       }

        //5.4. 技术服务费是否已完单后退还(之前是否处理过:完单后投诉入口进来的)
        List<TytTransportWaybillEx> transportWaybillExs = transportWaybillExService.getTransportWaybillExByOrderId(techRefundFinanceAudit.getOrderId(),1);
        logger.info("transportWaybillExService getTransportWaybillExByOrderId orderId:{} transportWaybillExs size:{}",techRefundFinanceAudit.getOrderId(),transportWaybillExs.size());
        if(CollectionUtils.isNotEmpty(transportWaybillExs)){
            for (TytTransportWaybillEx transportWaybillEx : transportWaybillExs) {
                TytTechRefundAudit tytTechRefundAudit = techRefundService.getTechRefundAuditByIdAndExIdAndStatus(techRefundFinanceAudit.getTechRefundAuditId(), transportWaybillEx.getId());
                if (Objects.nonNull(tytTechRefundAudit)) {
                    BigDecimal techRefundAuditAmount = tytTechRefundAudit.getRefundAmount();
                    if (techRefundAuditAmount != null && techRefundAuditAmount.compareTo(BigDecimal.ZERO) > 0) {
                        rm.setMsg("【投诉申请】完单退还技术服务费审核记录已存在，不允许重复处理操作");
                        rm.setCode(ReturnCodeConstant.ERROR);
                        return rm;
                    }
                }
            }
        }

        //5.5. 技术服务费是否已完单后退还(之前是否处理过:完单后申请退还入口进来的)
        TytTechRefundAudit tytTechRefundAuditOrders = techRefundService.getTechRefundAuditByOrderIdAndId(techRefundFinanceAudit.getTechRefundAuditId(),bean.getId());
        logger.info("techRefundService getTechRefundAuditByOrderIdAndId id:{} orderId:{} tytTechRefundAuditOrders:{}",techRefundFinanceAudit.getTechRefundAuditId(),bean.getId(), JSON.toJSONString(tytTechRefundAuditOrders));
        if(Objects.nonNull(tytTechRefundAuditOrders)){
            rm.setMsg("【完单申请】完单退还技术服务费审核记录已存在，不允许重复处理操作");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        return null;
    }


    /**
     * 技术服务费批量审核
     *
     * @param batchTecAuditReq
     * @param request
     * @return
     */
    @PostMapping("/batchAudit")
    public ResultMsgBean batchTecAudit(@RequestBody @Validated BatchTecAuditReq batchTecAuditReq,
                                       HttpServletRequest request) {
        logger.info("batchTecAudit batchTecAuditReq:{}", JSON.toJSONString(batchTecAuditReq));
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "更新成功");
        EmployeeQueryBean curUser = getCurrentUser(request);
        if (curUser == null) {
            rm.setMsg("未登录");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        try {
            String[] idsArray = StringUtils.split(batchTecAuditReq.getIds(), ",");
            for (String id : idsArray) {
                logger.info("batchTecAudit audit start id:{}", id);
                HttpServletRequest requestNew = new HttpServletRequestWrapper(request);
                ResultMsgBean resultMsgBean = audit(Long.valueOf(id), batchTecAuditReq.getAuditStatus(), requestNew);
                logger.info("batchTecAudit audit end id:{} result: {}", id, JSON.toJSONString(resultMsgBean));
                RedisUtil.set(Constant.TEC_BATCH_REFUND_CONSTANT + id, JSON.toJSONString(resultMsgBean), (int) TimeUnit.MINUTES.toSeconds(30));
            }
        } catch (Exception e) {
            logger.error("batchTecAudit async end batchTecAuditReq:{}" , JSON.toJSONString(batchTecAuditReq), e);
        }
        return rm;
    }


    /**
     * 查询技术服务费批量审核结果
     *
     * @param ids     审核id集合
     * @param request
     * @return
     */
    @PostMapping("/listBatchTecRefund")
    public ResultMsgBean listBatchTecRefund(@RequestParam(value = "ids", required = true) String ids,
                                            HttpServletRequest request) {
        logger.info("batchTecAudit listBatchTecRefund:{}", ids);
        if (StringUtils.isEmpty(ids)) {
            return new ResultMsgBean(ReturnCodeConstant.ERROR, "ids不能为空");
        }
        EmployeeQueryBean curUser = getCurrentUser(request);
        ResultMsgBean rm = new ResultMsgBean(ReturnCodeConstant.OK, "更新成功");
        if (curUser == null) {
            rm.setMsg("未登录");
            rm.setCode(ReturnCodeConstant.ERROR);
            return rm;
        }
        String[] idsArray = StringUtils.split(ids, ",");
        List<Long> successIds = new ArrayList<>();
        List<Long> failIds = new ArrayList<>();
        //从缓存中获取结果
        Arrays.stream(idsArray)
                .forEach(id -> {
                    String resultMsgBeanStr = RedisUtil.get(Constant.TEC_BATCH_REFUND_CONSTANT + id);
                    if (StringUtils.isBlank(resultMsgBeanStr)) {
                        return;
                    }
                    ResultMsgBean resultMsgBean = JSON.parseObject(resultMsgBeanStr, ResultMsgBean.class);
                    if (Objects.equals(Integer.valueOf(ResultMsgBean.OK), resultMsgBean.getCode())) {
                        successIds.add(Long.valueOf(id));
                    } else {
                        failIds.add(Long.valueOf(id));
                    }
                });
        //从缓存中获取结果
        BatchTechRefundResp batchTechRefundResp = new BatchTechRefundResp();
        batchTechRefundResp.setSuccessIds(successIds);
        batchTechRefundResp.setFailIds(failIds);
        return ResultMsgBean.successResponse(batchTechRefundResp);
    }

    /**
     * 失败完单退还导出
     *
     * @param ids
     * @param response
     */
    @GetMapping(value = "/exportFailTecRefundToExcel")
    public void exportFailTecRefundToExcel(@RequestParam(value = "ids", required = true) String ids, HttpServletResponse response, HttpServletRequest request) {
        logger.info("batchTecAudit listBatchTecRefund:{}", ids);
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = getCurrentUser(request);
            if (curUser == null) {
                return;
            }
            String name = "财务管理-完单退换技术服务费";
            List<BatchTecRefundResp> batchTecRefundRespList = new ArrayList<>();
            String[] idsArray = StringUtils.split(ids, ",");
            logger.info("batchTecAudit exportFailTecRefundToExcel ids:{} ", ids);
            Arrays.stream(idsArray)
                    .forEach(id -> {
                        String resultMsgBeanStr = RedisUtil.get(Constant.TEC_BATCH_REFUND_CONSTANT + id);
                        logger.info("batchTecAudit exportFailTecRefundToExcel id:{} result: {}", id, resultMsgBeanStr);
                        if (StringUtils.isBlank(resultMsgBeanStr)) {
                            return;
                        }
                        ResultMsgBean resultMsgBean = JSON.parseObject(resultMsgBeanStr, ResultMsgBean.class);
                        if (Objects.equals(Integer.valueOf(ResultMsgBean.OK), resultMsgBean.getCode())) {
                            return;
                        }
                        //0.根据完单退还技术服务费财务审核id查询财务审核信息
                        TytTechRefundFinanceAudit techRefundFinanceAudit = techRefundService.getTechRefundFinanceAuditById(Long.valueOf(id));
                        BatchTecRefundResp batchTecRefundResp = new BatchTecRefundResp();
                        if(Objects.nonNull(techRefundFinanceAudit)){
                            batchTecRefundResp.setUserId(techRefundFinanceAudit.getUserId() + "");
                            batchTecRefundResp.setTsOrderNo(techRefundFinanceAudit.getTsOrderNo());
                            batchTecRefundResp.setRefundAmount(techRefundFinanceAudit.getRefundAmount() + "");
                        }
                        batchTecRefundResp.setFailReason(resultMsgBean.getMsg());
                        batchTecRefundRespList.add(batchTecRefundResp);
                    });
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(name, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcelFactory.write(response.getOutputStream(), BatchTecRefundResp.class)
                    .sheet(name).doWrite(batchTecRefundRespList);
        } catch (Exception e) {
            logger.error("exportFailTecRefundToExcel error: ", e);
        }
    }
}
