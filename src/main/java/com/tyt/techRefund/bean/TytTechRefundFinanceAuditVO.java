package com.tyt.techRefund.bean;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
* description 完单退还技术服务费
* <AUTHOR>
* @since 2024/9/4 10:25
*/
@Data
public class TytTechRefundFinanceAuditVO implements Serializable {


    private Long id;

    /**
     * 退还技术服务费审核ID (tyt_tech_refund_audit表主键Id)
     */
    private Long techRefundAuditId;

    /**
     * 异常上报表ID
     */
    private Long exId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 用户手机号
     */
    private String userCellPhone;

    /**
     * 用户上报时间
     */
    private Date userApplyTime;

    /**
     * 运单号
     */
    private String tsOrderNo;

    /**
     * 退还金额 单位:元
     */
    private BigDecimal refundAmount;

    /**
     * 代调、抽佣钱包收款流水号
     */
    private String dispatchInFlowNo;

    /**
     * 代调、抽佣钱包出款流水号
     */
    private String dispatchOutFlowNo;

    /**
     * 财务审批状态 0.待审批 1.审批通过 2.审批拒绝
     */
    private Integer auditStatus;

    /**
     * 审核人员Id
     */
    private Long auditUserId;

    /**
     * 审核人员姓名
     */
    private String auditUserName;

    /**
     * 审核人员账户
     */
    private String auditCellPhone;

    /**
     * 审批时间
     */
    private Date auditTime;

    /**
     * 钱包类型：0.未知 1.代调钱包 2.抽佣钱包
     */
    private Integer walletType;

    /**
     * 退款状态 1.退款中 2.退款成功 3.退款失败
     */
    private Integer refundStatus;

    /**
     * 退款失败原因
     */
    private String refundErrMsg;

    /**
     * 是否代调：0.否 1.是
     */
    private Integer isDispatch;

    /**
     * 三方平台类型 
     * 0:特运通货源订单
     * 1:特运通货源，满帮订单
     * 2:满帮货源，特运通订单
     */
    private Integer thirdpartyPlatformType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 状态：1-已申请；2-开票中；3-已开票；4-未通过；5-已作废
     */
    private Integer status;


    /**
     * 状态：1-已申请；2-开票中；3-已开票；4-未通过；5-已作废 6-已红冲
     */
    private Integer applyDetailStatus;

    /**
     * 退还技术服务费申请渠道 0完单后投诉 1完单后申请退还入口 默认为0
     */
    private Integer refundTecChannel;
}