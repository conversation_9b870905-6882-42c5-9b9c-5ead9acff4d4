package com.tyt.techRefund.bean;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2025/05/20 11:38
 */
@Data
public class BatchTecRefundResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户id
     */
    @ExcelProperty("用户id")
    private String userId;

    /**
     * 运单号
     */
    @ExcelProperty("运单号")
    private String tsOrderNo;

    /**
     * 申请退还金额
     */
    @ExcelProperty("申请退还金额")
    private String refundAmount;

    /**
     * 失败原因
     */
    @ExcelProperty("失败原因")
    private String failReason;

}
