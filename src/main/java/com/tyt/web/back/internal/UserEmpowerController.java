package com.tyt.web.back.internal;

import com.tyt.jurisdiction.bean.EmployeeQueryBean;
import com.tyt.manager.entity.base.TytSyncgoodsRule;
import com.tyt.manager.service.dictionary.TytSyncgoodsRuleService;
import com.tyt.manager.service.dictionary.TytUserempowerSyncgoodsService;
import com.tyt.model.PageBean;
import com.tyt.model.ResultMsgBean;
import com.tyt.util.CsvWriter;
import com.tyt.web.back.internal.bean.SyncgoodsRuleVO;
import com.tyt.web.back.internal.bean.TytSyncgoodsRuleBean;
import com.tyt.web.base.BaseController;
import com.tyt.web.qbean.GroupUserlistQueryBean;
import com.tyt.web.qbean.TytUserempowerQueryBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName UserEmpowerController
 * @Description 用户同步货源授权
 * <AUTHOR> Lion
 * @Date 2022/10/14 9:38
 * @Verdion 1.0
 **/
@RestController
@RequestMapping("/boss/user/syncgoods")
public class UserEmpowerController extends BaseController {

    @Autowired
    private TytUserempowerSyncgoodsService tytUserempowerSyncgoodsService;

    @Autowired
    private TytSyncgoodsRuleService tytSyncgoodsRuleService;


    /**
     * @return void
     * <AUTHOR> Lion
     * @Description 权益用户导入接口  V6260
     * @Param [request, response, fileField]
     * @Date 2022/10/14 16:48
     */
    @RequestMapping(value = "/importList", method = RequestMethod.POST)
    public ResultMsgBean importList(HttpServletRequest request, HttpServletResponse response,
                                    @RequestParam(value = "fileField", required = true) MultipartFile fileField) {
        EmployeeQueryBean curUser = super.getRequireCurrentUser(request);
        ResultMsgBean msgBean = new ResultMsgBean(200, "导入成功");
        try {

            msgBean = tytUserempowerSyncgoodsService.saveExcel(fileField, curUser, msgBean);
        } catch (Exception e) {

            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("导入失败,请重新导入");
            logger.error("权益用户导入接口错误：", e);
        }
        return msgBean;
    }

    /**
     * 下载模板
     *
     * @param request  request
     * @param response response
     */
    @RequestMapping(value = "/excelExportModel", method = RequestMethod.POST)
    public void excelExportModel(HttpServletRequest request, HttpServletResponse response) {
        EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

        try {
            String header = "userId" + "\r\n";
            CsvWriter.exportCsv("权益用户导入标准模板", header.toString(), response);
            header = null;
        } catch (Exception e) {
            logger.error("excelExportModel错误：", e);
        }
    }


    /**
     * @return com.tyt.model.ResultMsgBean
     * <AUTHOR> Lion
     * @Description 用户列表查询接口(带条件 ， 数量等)  V6260
     * @Param [pageBean, tytUserempowerQueryBean, request, response]
     * @Date 2022/10/14 16:49
     */
    @RequestMapping("/getlist")
    @ResponseBody
    public ResultMsgBean getList(PageBean pageBean, TytUserempowerQueryBean tytUserempowerQueryBean, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean(200, "查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            if (pageBean.getCurrentPage() == 0 || pageBean.getPageSize() == 0) {
                result.setMsg("页数及每页条数不可为空");
                result.setCode(401);
                return result;
            }
            HashMap<String, Object> resultMap = tytUserempowerSyncgoodsService.getUserEmpowerList(pageBean, tytUserempowerQueryBean);
            result.setData(resultMap);
        } catch (Exception e) {
            logger.error("用户列表查询接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


    /***
     * <AUTHOR> Lion
     * @Description 货源同步配置模块'查询'接口
     * @Param [id, request, response]
     * @return com.tyt.model.ResultMsgBean
     * @Date 2022/10/17 14:40
     */
    @RequestMapping("/getrule")
    @ResponseBody
    public ResultMsgBean getrule(Long ruleGroupId, Integer excellentGoods, Integer publishType) {
        ResultMsgBean result = new ResultMsgBean(200, "查询成功");
        try {
            SyncgoodsRuleVO rule = tytSyncgoodsRuleService.findSyncGoodsRule(ruleGroupId, excellentGoods, publishType);
            result.setData(rule);
        } catch (Exception e) {
            logger.error("获取同步配置错误，入参{}，{}，{}：", ruleGroupId, excellentGoods, publishType, e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


    /**
     * @return com.tyt.model.ResultMsgBean
     * <AUTHOR> Lion
     * @Description 权益用户删除
     * @Param [id, request, response]
     * @Date 2022/10/14 16:49
     */
    @RequestMapping("/delete")
    @ResponseBody
    public ResultMsgBean deleteUserId(@RequestParam(value = "idlist", required = true) List<Long> idlist, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean(200, "删除成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            if (idlist == null || idlist.isEmpty()) {
                result.setMsg("idlist参数错误");
                result.setCode(402);
                return result;
            }
            result = tytUserempowerSyncgoodsService.deleteEmpowerUser(idlist, result);
        } catch (Exception e) {
            logger.error("权益用户删除接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


    /***
     * <AUTHOR> Lion
     * @Description 货源同步配置模块'修改启用/停用'接口
     * @Param [tytSyncgoodsRuleBean, request, response]
     * @return com.tyt.model.ResultMsgBean
     * @Date 2022/10/17 16:21
     */
    @RequestMapping("/update")
    @ResponseBody
    public ResultMsgBean updateRule(TytSyncgoodsRuleBean tytSyncgoodsRuleBean, HttpServletRequest request) {
        ResultMsgBean result = new ResultMsgBean(200, "操作成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            tytSyncgoodsRuleService.updateSyncgoodsRule(tytSyncgoodsRuleBean);
        } catch (Exception e) {
            logger.error("货源同步配置模块接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


    /**
     * @return void
     * <AUTHOR> Lion
     * @Description 分组用户导入接口
     * @Param [request, response, fileField, id]
     * @Date 2022/12/19 15:39
     */
    @RequestMapping(value = "/importGroupList", method = RequestMethod.POST)
    public void importGroupList(HttpServletRequest request, HttpServletResponse response,
                                @RequestParam(value = "fileField", required = true) MultipartFile fileField,
                                @RequestParam(value = "id", required = true) Long id) {
        EmployeeQueryBean curUser = super.getRequireCurrentUser(request);
        ResultMsgBean msgBean = new ResultMsgBean(200, "导入成功");
        try {
            msgBean = tytUserempowerSyncgoodsService.saveGroupExcel(fileField, curUser, msgBean, id);
        } catch (Exception e) {
            logger.error("分组用户导入接口错误：", e);
            msgBean.setCode(ResultMsgBean.ERROR);
            msgBean.setMsg("导入失败,请重新导入");
        }
        printJSON(request, response, msgBean);
    }


    @RequestMapping("/groupUserdelete")
    @ResponseBody
    public ResultMsgBean groupUserdelete(@RequestParam(value = "idlist", required = true) List<Long> idlist, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean(200, "删除成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            if (idlist == null || idlist.isEmpty()) {
                result.setMsg("idlist参数错误");
                result.setCode(402);
                return result;
            }
            result = tytUserempowerSyncgoodsService.deleteGroupUser(idlist, result);
        } catch (Exception e) {
            logger.error("分组用户删除接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }

    @RequestMapping("/getGroupUserlist")
    @ResponseBody
    public ResultMsgBean getGroupUserlist(PageBean pageBean, GroupUserlistQueryBean groupUserlistQueryBean, HttpServletRequest request, HttpServletResponse response) {
        ResultMsgBean result = new ResultMsgBean(200, "查询成功");
        try {
            // 获得当前用户的身份
            EmployeeQueryBean curUser = super.getRequireCurrentUser(request);

            if (pageBean.getCurrentPage() == 0 || pageBean.getPageSize() == 0) {
                result.setMsg("页数及每页条数不可为空");
                result.setCode(401);
                return result;
            }
            if (groupUserlistQueryBean.getRuleGroupId() == null) {
                result.setMsg("ruleGroupId不可为空");
                result.setCode(402);
                return result;
            }
            HashMap<String, Object> resultMap = tytUserempowerSyncgoodsService.getGroupUserlist(pageBean, groupUserlistQueryBean);
            result.setData(resultMap);
        } catch (Exception e) {
            logger.error("分组用户查询接口错误：", e);
            result.setCode(500);
            result.setMsg("服务器错误");
        }
        return result;
    }


}

先根据表结构写DO类，mapper接口，mapper xml文件，表结构如下
CREATE TABLE `tyt_syncgoods_definition_rule` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `rule_group_id` int(11) COMMENT '分组规则id（表tyt_syncgoods_rule_group）',

        -- 货源质量分字段（高值低值）
        `good_model_score_min` decimal(8,2) DEFAULT NULL COMMENT '货源质量分最小值(0-999，最多两位小数)',
        `good_model_score_max` decimal(8,2) DEFAULT NULL COMMENT '货源质量分最大值(0-999，最多两位小数)',

        -- 运距字段（高值低值）
        `distance_min` decimal(8,2) DEFAULT NULL COMMENT '运距最小值(0-9999，最多两位小数)',
        `distance_max` decimal(8,2) DEFAULT NULL COMMENT '运距最大值(0-9999，最多两位小数)',

        -- 货主身份类型（复选框，使用逗号分隔存储多选值）
        `transport_user_identity_type` tinyint(1) DEFAULT NULL COMMENT '货主身份类型(1:物流公司,2:货站,3:企业货主,4:个人货主，多选用逗号分隔)',

        -- 适用货源类型（三个字段，可多选）
        `apply_normal_goods` tinyint(1) DEFAULT 0 COMMENT '是否适用普货(0:否,1:是)',
        `apply_excellent_goods` tinyint(1) DEFAULT 0 COMMENT '是否适用优车1.0(0:否,1:是)',
        `apply_excellent_goods_two` tinyint(1) DEFAULT 0 COMMENT '是否适用优车2.0(0:否,1:是)',

        -- 价格模式（三个字段）
        `publish_type_fixed_price` tinyint(1) DEFAULT 0 COMMENT '价格模式-一口价(0:否,1:是)',
        `publish_type_no_price` tinyint(1) DEFAULT 0 COMMENT '价格模式-无价(0:否,1:是)',
        `publish_type_call_price` tinyint(1) DEFAULT 0 COMMENT '价格模式-电议有价(0:否,1:是)',

        -- 货源数据完整性
    `data_integrity_min` tinyint(1) DEFAULT NULL COMMENT '货源数据完整性最小值(0-4的整数)',

        -- 货源首发超过X分钟未成交
    `first_publish_timeout_minutes` int(3) NOT NULL COMMENT '货源首发超过X分钟未成交(0-999的整数，必填)',

        -- 反馈情况相关字段
    `enable_view_count_check` tinyint(1) DEFAULT 0 COMMENT '是否启用查看人数检查(0:否,1:是)',
        `view_count_timeout_minutes` int(4) DEFAULT NULL COMMENT '查看人数检查超时分钟数',
        `view_count_threshold` int(4) DEFAULT NULL COMMENT '查看人数阈值(≤Y)',

        `enable_call_count_check` tinyint(1) DEFAULT 0 COMMENT '是否启用拨打人数检查(0:否,1:是)',
        `call_count_timeout_minutes` int(4) DEFAULT NULL COMMENT '拨打人数检查超时分钟数',
        `call_count_threshold` int(4) DEFAULT NULL COMMENT '拨打人数阈值(≤Y)',

        -- 状态
    `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态(0:停用,1:启用)',

        -- 备注
    `remark` varchar(50) DEFAULT NULL COMMENT '备注(最大50字)',

        -- 创建时间
    `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `create_user_id` bigint(20) DEFAULT NULL COMMENT '创建人id',
        `create_username` varchar(50) DEFAULT NULL COMMENT '创建人username',

PRIMARY KEY (`id`),
KEY `idx_status` (`status`),
KEY `idx_ctime` (`ctime`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='同步货源定义规则表';

再帮我写几个接口
1.根据 rule_group_id、apply_normal_goods、apply_excellent_goods、apply_excellent_goods_two、publish_type_fixed_price、publish_type_no_price、publish_type_call_price、create_username（精确）、ctime时间范围
这些字段查询tyt_syncgoods_definition_rule列表，列表按ctime desc排序
2、


