package com.tyt.invoice.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;
import com.tyt.abtest.bean.PageData;
import com.tyt.acvitity.bean.CarLocation;
import com.tyt.common.bean.ShortMsgBean;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.infofee.bean.MqBaseMessageBean;
import com.tyt.infofee.bean.MqInfoFeeOperateMsg;
import com.tyt.internal.client.trade.ApiTradeServiceClient;
import com.tyt.invoice.bean.*;
import com.tyt.invoice.enums.InvoicePublicEnum;
import com.tyt.invoice.enums.OperateAppealEnum;
import com.tyt.invoice.enums.OrderStatusEnum;
import com.tyt.invoice.enums.SegmentPaymentEnum;
import com.tyt.invoice.service.InvoiceOperateService;
import com.tyt.invoice.utils.HaversineUtil;
import com.tyt.jurisdiction.service.EmployeeRoleCorrelationService;
import com.tyt.jurisdiction.service.EmployeeService;
import com.tyt.manager.entity.base.TytInvoiceDriver;
import com.tyt.manager.entity.base.TytOrderPicLogDetail;
import com.tyt.manager.entity.base.TytThirdFeePayment;
import com.tyt.manager.entity.base.TytTransportOrderSnapshot;
import com.tyt.manager.mapper.base.TytInvoiceDriverMapper;
import com.tyt.manager.mapper.base.TytOrderPicLogDetailMapper;
import com.tyt.manager.mapper.base.TytThirdFeePaymentMapper;
import com.tyt.manager.mapper.base.TytTransportOrderSnapshotMapper;
import com.tyt.manager.service.mq.MessageCenterPushService;
import com.tyt.model.ResultMsgBean;
import com.tyt.model.TytTransportOrders;
import com.tyt.model.User;
import com.tyt.mybatis.mapper.*;
import com.tyt.mybatis.mapper.model.*;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.service.config.TytConfigService;
import com.tyt.service.user.DriverCarPicService;
import com.tyt.service.user.UserService;
import com.tyt.util.Constant;
import com.tyt.util.LockUtil;
import com.tyt.util.SerialNumUtil;
import com.tyt.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static com.tyt.messagecenter.core.utils.CityUtil.toMapPointStr;

@Service
@Slf4j
public class InvoiceOperateServiceImpl implements InvoiceOperateService {

    @Resource
    private TransportProtocolMapper transportProtocolMapper;
    @Resource
    private TransportOrdersMapper transportOrdersMapper;
    @Resource
    private TytCarMapper tytCarMapper;
    @Resource
    private TransportMapper transportMapper;
    @Resource
    private OrderNodePicMapper orderNodePicMapper;

    @Resource
    private TytMqMessageService tytMqMessageService;

    @Resource
    private OrderOperateStatusMapper orderOperateStatusMapper;
    @Resource
    private TytConfigService tytConfigService;

    @Resource
    private TytInvoiceDriverMapper tytInvoiceDriverMapper;

    @Resource
    private MessageCenterPushService messageCenterPushService;

    @Resource
    private TytMessageTmplService tytMessageTmplService;
    @Resource
    private TytOrderOperateReasonMapper tytOrderOperateReasonMapper;

    @Resource
    EmployeeRoleCorrelationService employeeRoleCorrelationService;

    @Resource
    private OrderFreightMapper orderFreightMapper;

    @Resource
    private UserService userService;

    @Resource
    private TytOrderPicLogDetailMapper orderPicLogDetailMapper;

    @Resource
    private DriverCarPicService driverCarPicService;

    @Autowired
    private ApiTradeServiceClient apiTradeServiceClient;

    private ThirdFeePaymentMapper thirdFeePaymentMapper;

    @Autowired
    private TytTransportOrderSnapshotMapper orderSnapshotMapper;

    @Autowired
    private TytThirdFeePaymentMapper tytThirdFeePaymentMapper;
    @Autowired
    private ThreadPoolExecutor threadPoolExecutor;
    /**
     * 区域划分深度最大值
     */
    private static final int SOME_THRESHOLD = 12;

    /**
     * 轨迹点最小值
     */
    private static final int SOME_SMALL_SIZE = 1;

    public static final String ORDER_FIRST_PICK_GOODS_REDIS_KEY = "order:first:pick:goods:key:";

    public static final String ORDER_PICK_GOODS_REDIS_KEY = "order:pick:goods:key:";
    public static final String CREATE_ORDER_TIME = "create:order:time:";
    @Resource(name = "EmployeeService")
    private EmployeeService employeeService;
    @Override
    public PageData<TransportOrdersVO> queryList(ReqParam reqParam) {
        PageHelper.startPage(reqParam.getPageNum(), reqParam.getPageSize());
        String orderNewStatus = reqParam.getOrderNewStatus();
        String headNo = reqParam.getHeadNo();
        if (StringUtils.isNotBlank(headNo)) {
            char c = headNo.charAt(0);
            String headStr = Character.toString(c);
            String headNum = headNo.substring(1);
            reqParam.setHeadNum(headNum);
            reqParam.setHeadStr(headStr);
        }
        if (ObjectUtil.isNotEmpty(orderNewStatus) && orderNewStatus.contains(",")) {
            String[] split = orderNewStatus.split(",");
            reqParam.setOrderNewStatusTemp(split);
        } else {
            String orderNewStatus1 = reqParam.getOrderNewStatus();
            if (StringUtils.isNotBlank(orderNewStatus1)) {
                reqParam.setOrderNewStatusTemp(new String[]{orderNewStatus1});
            }
        }
        List<TransportOrdersVO> transportOrdersVOS = transportOrdersMapper.queryList(reqParam);
        for (TransportOrdersVO x : transportOrdersVOS) {
           // reqParam.setInnerSwitch(2);
            reqParam.setOrderId(x.getId());
            TransportProtocolVO one = transportProtocolMapper.getOne(reqParam);
            if (one != null) {
                x.setTotalCarriageFee(one.getTotalCarriageFee() / 100d);
                x.setAdditionalPrice(one.getAdditionalPrice() / 100);
                x.setPayToTerrace(one.getPayToTerrace() / 100);
            }
            //开票状态
/*
            Integer s2 = transportOrdersMapper.queryInvoiceOrder(x.getId());
*/
            /*x.setInvoiceStatus(s2);*/
            //运费支付时间
            reqParam.setOrderId(x.getId());
            reqParam.setOperateType(30);
/*
            List<OrderFreightVO> orderFreightVOS = orderFreightMapper.queryById(reqParam);
*/
            reqParam.setOpType(101);
/*
            OrderOperateStatusVO oneOperate = orderOperateStatusMapper.getOneOperate(reqParam);
*/
            List<OrderFreightVO> orderFreightVOS = orderFreightMapper.queryById(reqParam);
            if(CollectionUtils.isNotEmpty(orderFreightVOS)){
                x.setPayToDriver(orderFreightVOS.get(0).getPayTime());
            }
        }
        PageData<TransportOrdersVO> transportOrdersVOPageData = new PageData<>(transportOrdersVOS);
        return transportOrdersVOPageData;
    }

    @Override
    public InvoiceDetail dueDetail(ReqParam reqParam) {
        log.info("dueDetail is {}",reqParam);
        TransportOrdersVO transportOrdersVO = transportOrdersMapper.queryById(reqParam);
        // 详情企业名称
        String s1 = transportOrdersMapper.queryEnterpriseNameByUserId(transportOrdersVO.getUserId());
        Integer s2 = transportOrdersMapper.queryInvoiceOrder(transportOrdersVO.getId());
        List<OrderFreightVO> orderFreightVOS = orderFreightMapper.queryById(reqParam);
        CargoVO cargoVo = transportMapper.getCargoVo(transportOrdersVO.getTsId());
        //区分优车2.0
        if(cargoVo.getExcellentGoods() == 1){
            // 区分优车1.0  优车2.0
            String labelJson = cargoVo.getLabelJson();
            if(org.apache.commons.lang3.StringUtils.isNotBlank(labelJson)){
                JSONObject jsonObject = JSON.parseObject(labelJson);
                Integer goodCarPriceTransport = jsonObject.getInteger("goodCarPriceTransport");
                if(Objects.nonNull(goodCarPriceTransport) && goodCarPriceTransport == 1){
                    cargoVo.setExcellentGoods(11);
                }
            }
        }

        cargoVo.setTsId(reqParam.getTsId());
        List<TransportInfo> fileInfoDetail = orderNodePicMapper.getFileInfoList(reqParam);
        TransportInfo filePic = orderNodePicMapper.getFilePic(reqParam);
        InvoiceDetail invoiceDetail = new InvoiceDetail();
        //6600
        try {
            if(Objects.nonNull(reqParam)){
            log.info("getOrderSnapshot is {}",reqParam.getOrderId());
            TytTransportOrderSnapshot orderSnapshot = orderSnapshotMapper.getOrderSnapshot(reqParam.getOrderId());
            Integer segmentPayment = orderSnapshot.getSegmentPayment();
            if(segmentPayment != null && segmentPayment == 1){
                List<ThirdFeePaymentDO> feePaymentList = thirdFeePaymentMapper.getByOrderId(reqParam.getOrderId());
                if(CollectionUtils.isNotEmpty(feePaymentList)){
                    Map<String, ThirdFeePaymentDO> latestFeePaymentMap = feePaymentList.stream()
                            .collect(Collectors.groupingBy(
                                    ThirdFeePaymentDO::getFeeType,
                                    Collectors.collectingAndThen(
                                            Collectors.maxBy(Comparator.comparing(ThirdFeePaymentDO::getId)),
                                            Optional::get
                                    )
                            ));
                    List<ThirdFeePaymentDO> thirdFeePaymentDOS = new ArrayList<>(latestFeePaymentMap.values());
                    log.info("分段付詳情：{}",thirdFeePaymentDOS);
                    invoiceDetail.setListThirdFees(thirdFeePaymentDOS);
            }
            }
            }
        } catch (Exception e) {
            log.error("分段付查询异常：{}",e);
        }

        //订单基本信息
        BaseInfo baseInfo = new BaseInfo();
        baseInfo.setTel(transportOrdersVO.getTel());
        baseInfo.setPayLinkphone(transportOrdersVO.getPayCellPhone());
        baseInfo.setPayUserId(transportOrdersVO.getPayUserId());
        baseInfo.setUserId(transportOrdersVO.getUserId());
        baseInfo.setTsOrderNo(transportOrdersVO.getTsOrderNo());
        baseInfo.setOrderNewStatus(transportOrdersVO.getOrderNewStatus());
        baseInfo.setRiskControlStatus(transportOrdersVO.getRiskControlStatus());
        baseInfo.setInvoiceTransport(transportOrdersVO.getInvoiceTransport());
        baseInfo.setInvoiceStatus(s2);
        baseInfo.setEnterpriseName(s1);
        baseInfo.setFreightStatus(transportOrdersVO.getFreightStatus());
        baseInfo.setInvoiceThirdPartyNo(transportOrdersVO.getInvoiceThirdPartyNo());
        baseInfo.setInvoiceServiceCode(transportOrdersVO.getInvoiceServiceCode());
        User  goodsUser = userService.getById(transportOrdersVO.getUserId());
        baseInfo.setGoodsUserName(Objects.isNull(goodsUser)?"":goodsUser.getTrueName());
        User  carUser = userService.getById(transportOrdersVO.getPayUserId());
        baseInfo.setCarUserName(Objects.isNull(carUser)?"":carUser.getTrueName());
        if (filePic != null && filePic.getGoodsInsurancePicUrl() != null) {
            baseInfo.setIsUploadGoodsInsurancePicUrl(1);
        } else {
            baseInfo.setIsUploadGoodsInsurancePicUrl(0);
        }
        //是否是开票指派单（0:否 1:是）
        baseInfo.setIsAssignOrder(transportOrdersVO.getIsAssignOrder());

        // 起运截止时间 第一次起运+20小时
        String loadFirstTime = RedisUtil.get(ORDER_FIRST_PICK_GOODS_REDIS_KEY+transportOrdersVO.getId());
        if(StringUtils.isNotBlank(loadFirstTime)){
            baseInfo.setLoadDeadline(TimeUtil.addMinute(TimeUtil.parseDate(loadFirstTime),20*60));
        }

        // 起运成功时间
        String loadSuccessTime = RedisUtil.get(ORDER_PICK_GOODS_REDIS_KEY+transportOrdersVO.getId());
        if(StringUtils.isBlank(loadSuccessTime)){
            loadSuccessTime = RedisUtil.get(ORDER_FIRST_PICK_GOODS_REDIS_KEY+transportOrdersVO.getId());
        }
        if(StringUtils.isNotBlank(loadSuccessTime)){
            // 卸货截止时间
            baseInfo.setUnLoadDeadline(TimeUtil.addDays(TimeUtil.parseDate(loadSuccessTime),30));
        }

        //OrderOperateStatusVO byOrderIdOperateType = orderOperateStatusMapper.getByOrderIdOperateType(transportOrdersVO.getId(), 22);
        String createOrderTimeStr = RedisUtil.get(CREATE_ORDER_TIME + transportOrdersVO.getId());
        if(StringUtils.isNotBlank(createOrderTimeStr)){
            // 装货可选时间
            baseInfo.setLoadStartTime(TimeUtil.addMinute(TimeUtil.parseDate(createOrderTimeStr),-10));
            baseInfo.setLoadEndTime(TimeUtil.addMinute(TimeUtil.addDays(TimeUtil.parseDate(createOrderTimeStr),4),-10));
        }else {
            // 装货可选时间
            baseInfo.setLoadStartTime(transportOrdersVO.getCreateTime());
            baseInfo.setLoadEndTime(TimeUtil.addDays(transportOrdersVO.getCreateTime(),4));
        }

        invoiceDetail.setBaseInfo(baseInfo);
        // 订金处理
        CostInfo costInfo = new CostInfo();
        String infoFee = cargoVo.getInfoFee();
        if (infoFee == null) {
            infoFee = "";
        }
        Integer refundFlag = transportOrdersVO.getRefundFlag();
        String refundStr = refundFlag == 0 ? "不退还" : "退还";
        String s = infoFee + "(" + refundStr + ")";
        costInfo.setInfoFee(s);
        costInfo.setCostStatus(transportOrdersVO.getCostStatus());
        costInfo.setPayAmount(transportOrdersVO.getPayAmount() / 100);
        costInfo.setReductionAmount(transportOrdersVO.getCouponAmount() / 100);
        costInfo.setRefundFlag(refundFlag);
        costInfo.setTecFee(transportOrdersVO.getTecServiceFee() / 100);
        costInfo.setTecFeeStatus(transportOrdersVO.getPayStatus());
        if(CollectionUtils.isNotEmpty(orderFreightVOS)){
            double amount = 0;
            for(OrderFreightVO of : orderFreightVOS){
                amount+=of.getAmount();
            }
            costInfo.setPayToTerrace(String.format("%.2f",amount));
        }
      /*  if(orderFreightVO != null){
            costInfo.setTradeNo(orderFreightVO.getTradeNo());
        }*/
        List<TransportProtocolVO> list = transportProtocolMapper.getList(reqParam);
        // 计算次数
        if (CollectionUtils.isNotEmpty(list)) {
            Map<Long, List<TransportProtocolVO>> collect = list.stream().collect(Collectors.groupingBy(TransportProtocolVO::getCarriageFee));
            int size = collect.size();
            if (size > 0) {
                costInfo.setEditCount(size - 1);
            }
        }
        // 货源信息
        invoiceDetail.setCargoVO(cargoVo);
        // 承运信息
        AcceptForCarriageInfo acf = new AcceptForCarriageInfo();
        if (ObjectUtil.isNotEmpty(reqParam.getCarId())) {
            acf = tytCarMapper.queryCarInfo(reqParam);

            long weightTemp = 0l;
            if (acf != null) {
                String headCurbWeight = acf.getHeadCurbWeight();
                String tailCurbWeight = acf.getTailCurbWeight();
                Long headCurbWeight1 = 0l;
                Long tailCurbWeight1 = 0l;

                if(headCurbWeight.length()<=2){
                    headCurbWeight1=0l;
                }else{
                    headCurbWeight1 = Long.valueOf(headCurbWeight);
                }
                if(tailCurbWeight.length()<=2){
                    tailCurbWeight1=0l;
                }else{
                    tailCurbWeight1 = Long.valueOf(tailCurbWeight);
                }
                Double weight = Double.valueOf(cargoVo.getWeight());
                Long totalWeight = 0l;
                if (headCurbWeight != null) {
                    weightTemp += headCurbWeight1 / 1000;
                    totalWeight += headCurbWeight1;
                }
                if (tailCurbWeight != null) {
                    weightTemp += tailCurbWeight1 / 1000;
                    totalWeight += tailCurbWeight1;
                }
                if (weight != null) {
                    weightTemp += weight;
                    double v1 = weight * 1000;
                    Long v = Long.valueOf((long) v1);
                    totalWeight += v;
                }
                acf.setTotalWeight(totalWeight);
                if (weightTemp > 49) {
                    acf.setOverLoad(true);
                }
            }
        }

        TytInvoiceDriver byId = tytInvoiceDriverMapper.findById(transportOrdersVO.getDriverId());
        if (acf == null) {
            acf = new AcceptForCarriageInfo();
        }
        if (byId != null) {
            acf.setDriverName(byId.getName());
            acf.setDriverPhoneNumber(byId.getPhone());
        }
        log.info("transportVo data is :{}",transportOrdersVO);
        acf.setLicensePlateNumber(transportOrdersVO.getHeadCity()+transportOrdersVO.getHeadNo()+" " +transportOrdersVO.getTailCity()+transportOrdersVO.getTailNo());
        String maxAxlesOne = acf.getMaxAxlesOne();
        String maxAxlesTwo = acf.getMaxAxlesTwo();
        String maxAxles = "";
        StringBuffer sb = new StringBuffer();
        if (maxAxlesOne != null) {
            maxAxles = maxAxlesOne + "kg";
            sb.append(maxAxles);
        } else {
            sb.append("0kg");
        }
        if (maxAxlesTwo != null) {
            maxAxles = maxAxlesTwo + "kg";
            sb.append("+");
            sb.append(maxAxles);
        } else {
            sb.append("+");
            sb.append("0kg");
        }
        acf.setMaxAxles(sb.toString());
        // acf.setDriverName(byId.getName());
        acf.setDriverId(transportOrdersVO.getDriverId());
        acf.setCarId(transportOrdersVO.getCarId());
        TransportProtocolVO one = transportProtocolMapper.getOne(reqParam);
        int protocolStatus = 0;
        if (one != null) {
            costInfo.setCarriageFee(one.getCarriageFee() / 100);//司机运费
            costInfo.setAdditionalPrice(one.getAdditionalPrice() / 100d);//附加运费
/*
            costInfo.setPayToTerrace(one.getPayToTerrace() / 100);
*/
            double v = one.getTotalCarriageFee() / 100d;
            costInfo.setTotalCarriageFee(v);//运费
            protocolStatus = one.getProtocolStatus();
            acf.setProtocolStatus(one.getProtocolStatus());
            costInfo.setEnterpriseTaxRate(one.getEnterpriseTaxRate());
            // acf.setDriverId(one.getDriverId());
            //  acf.setDriverName(one.getDriverName());
        }
        if (protocolStatus == 2) {
            reqParam.setInnerSwitch(2);
            TransportProtocolVO two = transportProtocolMapper.getOne(reqParam);
            if(two != null){
                acf.setContractSignUrl(two.getContractSignUrl());
                acf.setProtocolName(two.getProtocolName());
            }
            reqParam.setInnerSwitch(3);
            TransportProtocolVO three = transportProtocolMapper.getOne(reqParam);
            if(three != null){
                acf.setShipperContractSignUrl(three.getContractSignUrl());
                acf.setShipperProtocolName(three.getProtocolName());
            }
        }
        invoiceDetail.setAcceptForCarriageInfo(acf);
        reqParam.setOperateType(16);
        reqParam.setDataType(0);
        List<OrderOperateStatusVO> list1 = orderOperateStatusMapper.getList(reqParam);

        //文件上传信息
        invoiceDetail.setListOps(list1);
        //运单照片是否上报三方
        fileInfoIsReport(fileInfoDetail);
        invoiceDetail.setTransportInfo(fileInfoDetail);
        invoiceDetail.setCostInfo(costInfo);
        //分段付 金额
        TytTransportOrderSnapshot orderSnapshot = orderSnapshotMapper.getOrderSnapshot(transportOrdersVO.getId());
        setTytThirdFeePaymentVOList(orderSnapshot, invoiceDetail, transportOrdersVO);
        return invoiceDetail;
    }

    /**
     * 分段付
     * @param orderSnapshot
     * @param invoiceDetail
     * @param transportOrdersVO
     */
    private void setTytThirdFeePaymentVOList(TytTransportOrderSnapshot orderSnapshot, InvoiceDetail invoiceDetail, TransportOrdersVO transportOrdersVO) {
        if(Objects.equals(SegmentPaymentEnum.SUPPORTED.getCode(), orderSnapshot.getSegmentPayment())){
            invoiceDetail.setSegmentPayment(SegmentPaymentEnum.SUPPORTED.getCode());
            List<TytThirdFeePayment> thirdFeePaymentList = getLatestByOrderId(transportOrdersVO.getId());
            List<TytThirdFeePaymentVO> thirdFeePaymentVOList = thirdFeePaymentList.stream().map(feePay -> {
                TytThirdFeePaymentVO feePayVO = new TytThirdFeePaymentVO();
                BeanUtils.copyProperties(feePay, feePayVO);
                return feePayVO;
            }).collect(Collectors.toList());
            invoiceDetail.setTytThirdFeePaymentVOList(thirdFeePaymentVOList);
        }
    }

    public List<TytThirdFeePayment> getLatestByOrderId(Long orderId) {
        List<TytThirdFeePayment> feePaymentList = tytThirdFeePaymentMapper.getByOrderId(orderId);
        if(CollUtil.isEmpty(feePaymentList)){
            return new ArrayList<>();
        }
        Map<String, TytThirdFeePayment> latestFeePaymentMap = feePaymentList.stream()
                .collect(Collectors.groupingBy(
                        TytThirdFeePayment::getFeeType,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(TytThirdFeePayment::getId)),
                                Optional::get
                        )
                ));
        return new ArrayList<>(latestFeePaymentMap.values());
    }

    /**
     * 运单照片是否上报三方
     *
     * <AUTHOR>
     * @param fileInfoDetail
     * @return void
     */
    private void fileInfoIsReport(List<TransportInfo> fileInfoDetail) {
        for (TransportInfo transportInfo : fileInfoDetail) {
            String loadPicUrl = transportInfo.getLoadPicUrl();
            if(StringUtils.isNotBlank(loadPicUrl)){
                String newLoadPicUrl = Arrays.stream(loadPicUrl.split(","))
                        .map(url -> url + "#" + singleImageIsReport(url))
                        .collect(Collectors.joining(","));

                transportInfo.setLoadPicUrl(newLoadPicUrl);
            }

            String unLoadPicUrl = transportInfo.getUnLoadPicUrl();
            if(StringUtils.isNotBlank(unLoadPicUrl)){
                String newUnLoadPicUrl = Arrays.stream(unLoadPicUrl.split(","))
                        .map(url -> url + "#" + singleImageIsReport(url))
                        .collect(Collectors.joining(","));

                transportInfo.setUnLoadPicUrl(newUnLoadPicUrl);
            }

            String receiptPicUrl = transportInfo.getReceiptPicUrl();
            if(StringUtils.isNotBlank(receiptPicUrl)){
                String newReceiptPicUrl = Arrays.stream(receiptPicUrl.split(","))
                        .map(url -> url + "#" + singleImageIsReport(url))
                        .collect(Collectors.joining(","));

                transportInfo.setReceiptPicUrl(newReceiptPicUrl);
            }

            String outLimitCertificatePicUrl = transportInfo.getOutLimitCertificatePicUrl();
            if(StringUtils.isNotBlank(outLimitCertificatePicUrl)){
                String newOutLimitCertificatePicUrl = Arrays.stream(outLimitCertificatePicUrl.split(","))
                        .map(url -> url + "#" + singleImageIsReport(url))
                        .collect(Collectors.joining(","));

                transportInfo.setOutLimitCertificatePicUrl(newOutLimitCertificatePicUrl);
            }

            String riskAppealPicUrl = transportInfo.getRiskAppealPicUrl();
            if(StringUtils.isNotBlank(riskAppealPicUrl)){
                String newRiskAppealPicUrl = Arrays.stream(riskAppealPicUrl.split(","))
                        .map(url -> url + "#" + singleImageIsReport(url))
                        .collect(Collectors.joining(","));

                transportInfo.setRiskAppealPicUrl(newRiskAppealPicUrl);
            }

            String goodsInsurancePicUrl = transportInfo.getGoodsInsurancePicUrl();
            if(StringUtils.isNotBlank(goodsInsurancePicUrl)){
                String newGoodsInsurancePicUrl = Arrays.stream(goodsInsurancePicUrl.split(","))
                        .map(url -> url + "#" + singleImageIsReport(url))
                        .collect(Collectors.joining(","));

                transportInfo.setGoodsInsurancePicUrl(newGoodsInsurancePicUrl);
            }

            //查询人车合照信息
            transportInfo.setDriverCarUrl(buildDriverCarUrl(transportInfo.getOrderId()));
        }
    }

    private String buildDriverCarUrl(Long orderId) {
        ReqParam reqParam = new ReqParam();
        reqParam.setOrderId(orderId);
        TransportOrdersVO transportOrdersVO = transportOrdersMapper.queryById(reqParam);
        DriverCarPicDO driverCarPicDO = driverCarPicService.get(transportOrdersVO.getDriverId(), transportOrdersVO.getCarId());
        if (ObjectUtil.isNotNull(driverCarPicDO)) {
            return driverCarPicDO.getDriverCarUrl() + "#" + driverCarPicDO.getAuditStatus();
        }
        return null;
    }

    private Integer singleImageIsReport(String imageUrl){
        int isReport = 0;
        List<TytOrderPicLogDetail> orderPicLogDetails = orderPicLogDetailMapper.selectByImageUrlAndStatus(imageUrl);
        if(CollectionUtils.isNotEmpty(orderPicLogDetails)){
            isReport =  1;
        }
        return isReport;
    }

    @Override
    public List<TransportInfo> getFileInfoDetail(ReqParam reqParam) {
        String urlTypeStr = reqParam.getUrlTypeStr();
        List<TransportInfo> fileInfoDetail = orderNodePicMapper.getFileInfoDetail(reqParam);

        return orderNodePicMapper.getFileInfoDetail(reqParam);
    }

    @Override
    public List<TransportProtocolVO> getProtocolInfo(ReqParam reqParam) {
        return transportProtocolMapper.getList(reqParam);
    }


    /**
     * 异步发送钉钉消息
     *
     * @param type 修改的资源类型 1:开关；2:公共资源；3:ab测试
     * @param message 钉钉消息内容
     */

    @Override
    public void saveOperate(ReqParam reqParam) {
        try {
            transportOrdersMapper.operateOrders(reqParam);
            threadPoolExecutor.execute(() -> {
                TransportOrdersVO transportOrdersVO = transportOrdersMapper.queryById(reqParam);
                OrderOperateStatusVO os = new OrderOperateStatusVO();
                os.setOrderId(reqParam.getOrderId());
                os.setCreateTime(new Date());
                os.setOrderStatus(transportOrdersVO.getOrderNewStatus());
                os.setCostStatus(transportOrdersVO.getCostStatus());
                int operate = 0;
                if (reqParam.getOperate() == 1) {
                    operate = 40;
                } else {
                    operate = 45; //申诉失败
                }
                os.setOperateType(operate);
                os.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
                os.setMessageType(1077);
                log.info("【审核操作】记录：{}", JSON.toJSONString(os));
                tytMqMessageService.addMqMessage(os.getMessageSerailNum(), JSON.toJSONString(os), os.getMessageType());
                tytMqMessageService.sendMqMessage(os.getMessageSerailNum(), JSON.toJSONString(os));
            });
        } catch (Exception e) {
            log.error("【订单操作记录】发生异常：{}", e);
        }
    }

    @Override
    public LocusInfo getLocusInfo(ReqParam reqParam) {
        LocusInfo lf = new LocusInfo();
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("orderId", reqParam.getOrderId());
        String envSwitch = tytConfigService.getStringValue("env_switch");
        CargoVO cargoVo = transportMapper.getCargoVo(reqParam.getTsId());
        if (ObjectUtil.isNotEmpty(cargoVo)) {
            lf.setOrderId(reqParam.getOrderId());
            lf.setGoodsStartLongitude(toMapPointStr(cargoVo.getStartLongitude()));
            lf.setGoodsStartLatitude(toMapPointStr(cargoVo.getStartLatitude()));
            lf.setGoodsDestLongitude(toMapPointStr(cargoVo.getDestLongitude()));
            lf.setGoodsDestLatitude(toMapPointStr(cargoVo.getDestLatitude()));
            lf.setExpectLoadTime(cargoVo.getLoadingTime());
            lf.setExpectUnLoadTime(cargoVo.getUnloadTime());
        }
        String result = HttpUtil.get(envSwitch + "/trade-web/trackReport/getCarTracksByOrderId", paramMap);
        ResultMsgBean resp = JSON.parseObject(result, ResultMsgBean.class);
        List<OrderTrace> data = (List<OrderTrace>) resp.getData();
        log.info("【轨迹查询】返回点：{}",data);
        /*reqParam.setOperateType(25);
        reqParam.setDataType(0);
        // 获取 自动 装货计算风控位置
        OrderOperateStatusVO oneOperateLoad = orderOperateStatusMapper.getOneOperate(reqParam);
        // 获取 自动 卸货计算风控位置
        reqParam.setOperateType(30);
        OrderOperateStatusVO oneOperateUnLoad = orderOperateStatusMapper.getOneOperate(reqParam);
        log.info("【轨迹信息】装货：{},卸货：{}",oneOperateLoad,oneOperateUnLoad);*/
/*
        if(ObjectUtil.isNotNull(oneOperateLoad)){
*/
          /*  Double startLatitude = oneOperateLoad.getStartLatitude();
            Double startLongitude = oneOperateLoad.getStartLongitude();
            if(startLatitude== null && startLongitude== null){*/
                reqParam.setDataType(2);
                reqParam.setOperateType(25);
                List<OrderOperateStatusVO> list = orderOperateStatusMapper.getList(reqParam);
                log.info("【轨迹计算】手动装货 list：{}",list);
                if(CollectionUtils.isNotEmpty(list)){
                    OrderOperateStatusVO operateStatusVO = list.get(0);
                    log.info("【轨迹计算】手动装货 对象：{}",operateStatusVO);
                    if(operateStatusVO != null && operateStatusVO.getStartLatitude()!= null){
                        lf.setActualityLoadTime(operateStatusVO.getCreateTime());
                        lf.setActualityStartLat(operateStatusVO.getStartLatitude());
                        lf.setActualityStartLon(operateStatusVO.getStartLongitude());
                    }/*else{
                        lf.setActualityLoadTime(oneOperateLoad.getCreateTime());
                        lf.setActualityStartLat(startLatitude);
                        lf.setActualityStartLon(startLongitude);
                    }*/
                }
            /*}*/
        /*}*/
/*
        if(ObjectUtil.isNotNull(oneOperateUnLoad)){
*/
           /* Double destLatitude = oneOperateUnLoad.getDestLatitude();
            Double destLongitude = oneOperateUnLoad.getDestLongitude();
            if(destLatitude != null && destLongitude!= null){*/
                reqParam.setDataType(1);
                reqParam.setOperateType(16);
                List<OrderOperateStatusVO> list1 = orderOperateStatusMapper.getList(reqParam);
                log.info("【轨迹计算】手动卸货 list：{}",list1);
                if(CollectionUtils.isNotEmpty(list1)){
                    OrderOperateStatusVO operateStatusVO = list1.get(0);
                    log.info("【轨迹计算】手动卸货 对象：{}",operateStatusVO);
                    if(operateStatusVO != null && operateStatusVO.getDestLongitude()!= null){
                        lf.setActualityUnloadTime(operateStatusVO.getCreateTime());
                        lf.setActualityDestLat(operateStatusVO.getDestLatitude());
                        lf.setActualityDestLon(operateStatusVO.getDestLongitude());
                    }/*else{
                        lf.setActualityLoadTime(oneOperateUnLoad.getCreateTime());
                        lf.setActualityStartLat(destLatitude);
                        lf.setActualityStartLon(destLongitude);
                    }
                }*/
            }
       /* }*/

        lf.setListT(data);
        log.info("轨迹查询返回：{}", lf);
        return lf;
    }
    @Override
    public ResultMsgBean updateConvertNormalOrder(ReqParam reqParam) {
        TransportOrdersVO transportOrdersVO = transportOrdersMapper.queryById(reqParam);
        if(transportOrdersVO != null && transportOrdersVO.getInvoiceTransport() == InvoicePublicEnum.invoiceNo.getId()){
            throw new RuntimeException("当前订单为非专票订单，无需转普通订单!");
        }else if(transportOrdersVO.getCostStatus()==25){
            return new ResultMsgBean(5001,"当前订单状态为异常上报处理中，不能转普通单");
        }
        Integer orderNewStatus = transportOrdersVO.getOrderNewStatus();
        if(orderNewStatus == OrderStatusEnum.WAIT_SIGN.getCode()){
            reqParam.setOrderNewStatus(OrderStatusEnum.WAIT_LOADED.getCode()+"");
        }
        else if(orderNewStatus == OrderStatusEnum.WAIT_COLLECT_OR_PAY_FREIGHT.getCode()){
            reqParam.setOrderNewStatus(OrderStatusEnum.FINISH.getCode()+"");
        }else {
            reqParam.setOrderNewStatus(transportOrdersVO.getOrderNewStatus()+"");
        }
        if (transportOrdersVO.getFreightStatus() != null
                && transportOrdersVO.getFreightStatus() > 0) {
            reqParam.setFreightStatus(30);
            //发送退还运费MQ
            tytMqMessageService.sendMsgToRefundFreight(reqParam.getOrderId());
        }
        transportOrdersMapper.updateTransportInvoiceById(reqParam);
        insertReason(reqParam,1);
        //短信内容
        /**
         *  等业务方 模板
         */
     /*   String reason = reqParam.getReason();
        String strSMS = "您申请提现的${tixian}元已到账，提现银行卡：${yinhang}尾号${weihao}，请注意查收。";
        //String content = tytMessageTmplService.getSmsTmpl(smsTempEnum.getTempKey(), smsTempEnum.getDefaultContent());
        String smsContent = StringUtils.replaceEach(strSMS, new String[]{"${tixian}","${yinhang}","${weihao}"}, new String[]{"1000","SSS","4542"});
        if (StringUtils.isNotBlank(smsContent)) {
            log.info("变更手机号审核，发送短信内容为:{}", smsContent);
            messageCenterPushService.sendShortMsg(transportOrdersVO.getPayCellPhone(),smsContent,"");
        }*/
        //
        CompletableFuture.runAsync(() -> {
            TytTransportOrders transportOrders = new TytTransportOrders();
            BeanUtils.copyProperties(transportOrdersVO, transportOrders);
            apiTradeServiceClient.cancelThirdOrder(transportOrders);
        }).whenComplete((v, t) -> {
            log.info("updateConvertNormalOrder orderId：{}", transportOrdersVO.getId(), t);
        });

        return new ResultMsgBean(200,"操作成功");
    }
    private void insertReason(ReqParam reqParam,int type){
        try {
            TytOrderOperateReason tytOrderOperateReason = new TytOrderOperateReason();
            tytOrderOperateReason.setReason(reqParam.getReason());
            tytOrderOperateReason.setOrderId(reqParam.getOrderId());
            tytOrderOperateReason.setType(type);
            tytOrderOperateReasonMapper.insert(tytOrderOperateReason);
        } catch (Exception e) {
            log.error("【订单操作】记录转普通订单/虚假单出现异常：",e);
        }
    }
    @Override
    public void saveOperateAppealOrder(ReqParam reqParam) {
        // transportOrdersMapper.operateOrders(reqParam);
        TransportOrdersVO transportOrder = transportOrdersMapper.queryById(reqParam);
        try {
                //虚假单取消订单，并退还订金
                int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
                if (LockUtil.lockObject("risk_order_cancel", reqParam.getOrderId() + "", redisLockTimeout)) {
                    log.info("risk order time out,refund task get redis lock success, order id is: " + reqParam.getOrderId());
                    if (transportOrder != null) {
                       this.saveUpdateRiskOrderStatus(transportOrder,reqParam);
                    }
                }
        }catch (Exception e) {
            log.error("risk order time out,refund task order id:{},error:{}", reqParam.getOrderId(), e);
        } finally {
            log.info("risk order time out,refund task release redis lock begin, order id is: " + reqParam.getOrderId());
            LockUtil.unLockObject("4", reqParam.getOrderId() + "");
        }
        // 发送短信
        addSendSms(transportOrder,reqParam);
    }

    @Override
    public TrackCalculateVO TrackCalculate(ReqParam reqParam,List<CarLocation> listC) {
        log.info("【轨迹打点】：{}",JSON.toJSONString(listC));
        TransportOrdersVO transportOrdersVO = transportOrdersMapper.queryById(reqParam);
        CargoVO cargoVo = transportMapper.getCargoVo(transportOrdersVO.getTsId());
        log.info("[cargo] is {}",cargoVo);
        if(transportOrdersVO == null){
            log.info("【计算轨迹】订单不存在！");
            return new TrackCalculateVO();
        }
        if(cargoVo == null){
            log.info("【计算轨迹】货源不存在！");
            return new TrackCalculateVO();
        }
        // 装货地 经纬度
        TrackCalculateVO tcv = new TrackCalculateVO();
        DestPointBean destPointBean = new DestPointBean();
        Double startLon = Double.valueOf(toMapPointStr(cargoVo.getStartLongitude()));
        Double startLat = Double.valueOf(toMapPointStr(cargoVo.getStartLatitude()));
        tcv.setExpectLoadTime(cargoVo.getLoadingTime());
        tcv.setExpectUnLoadTime(cargoVo.getUnloadTime());
        destPointBean.setLatitude(startLat);
        destPointBean.setLongitude(startLon);
        log.info("destPointBean1：{},{}",startLon,startLat);
        OrderTraceBean nearestPoint = findNearestPoint(destPointBean, listC);
        OrderOperateStatusVO operateStatusVO = new OrderOperateStatusVO();

        if(nearestPoint != null){
            reqParam.setDataType(1);
            operateStatusVO.setOperateType(16);
            operateStatusVO.setDataType(1);
            operateStatusVO.setOrderId(reqParam.getOrderId());
            operateStatusVO.setStartLatitude(nearestPoint.getLatitude());
            operateStatusVO.setStartLongitude(nearestPoint.getLongitude());
            operateStatusVO.setCreateTime(nearestPoint.getUploadTime());
            reqParam.setOperateType(16);
            List<OrderOperateStatusVO> list = orderOperateStatusMapper.getList(reqParam);
            if(CollectionUtils.isEmpty(list)){
                orderOperateStatusMapper.insertOps(operateStatusVO);
            }else {
                orderOperateStatusMapper.updateActuality(operateStatusVO);
            }
            tcv.setActualityLoadTime(nearestPoint.getUploadTime());
            tcv.setActualityStartLat(nearestPoint.getLatitude());
            tcv.setActualityStartLon(nearestPoint.getLongitude());
        }
        log.info("【轨迹计算】 装货地点实际计算所得：{},更新内容：{}",nearestPoint,operateStatusVO);
        DestPointBean destPointBeanDest = new DestPointBean();
        Double destLon = Double.valueOf(toMapPointStr(cargoVo.getDestLongitude()));
        Double destLat = Double.valueOf(toMapPointStr(cargoVo.getDestLatitude()));
        destPointBeanDest.setLatitude(destLat);
        destPointBeanDest.setLongitude(destLon);
        log.info("destPointBean2：{},{}",destLon,destLat);
        OrderTraceBean nearestPointDest = findNearestPoint(destPointBeanDest, listC);
        if(nearestPointDest != null){
            operateStatusVO.setDataType(2);
            reqParam.setDataType(2);
            operateStatusVO.setOperateType(30);
            operateStatusVO.setOrderId(reqParam.getOrderId());
            operateStatusVO.setDestLatitude(nearestPointDest.getLatitude());
            operateStatusVO.setDestLongitude(nearestPointDest.getLongitude());
            operateStatusVO.setCreateTime(nearestPointDest.getUploadTime());
            reqParam.setOperateType(30);
            List<OrderOperateStatusVO> list = orderOperateStatusMapper.getList(reqParam);
            if(CollectionUtils.isEmpty(list)){
                orderOperateStatusMapper.insertOps(operateStatusVO);
            }else {
                orderOperateStatusMapper.updateActuality(operateStatusVO);
            }
            tcv.setActualityUnloadTime(nearestPointDest.getUploadTime());
            tcv.setActualityDestLat(nearestPointDest.getLatitude());
            tcv.setActualityDestLon(nearestPointDest.getLongitude());
        }
        log.info("【轨迹计算】 卸货地点实际计算所得：{},更新内容：{}",nearestPointDest,operateStatusVO);

        log.info("【轨迹计算】 实际返回内容：{}",tcv);
        return tcv;
    }

    /**
     *
     * @param destPoint 目标（对比）轨迹点
     * @param orderPoints 所有轨迹点
     * @return
     */
    private static OrderTraceBean findNearestPoint(DestPointBean destPoint, List<CarLocation> orderPoints) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //声明最近的订单轨迹点
        OrderTraceBean orderTraceBean = new OrderTraceBean();
        //情况一:当轨迹点数<=1时
        if (orderPoints.size() <= 1) {
            //当轨迹点数量为1时 内容为空返回null
            CarLocation nearest = orderPoints.get(0);
            log.info("findNearestPoint nearest:{}", nearest==null?"":JSON.toJSONString(nearest));
            if (ObjectUtil.isNull(nearest)|| nearest.getLat() == null || nearest.getLon() == null){
                return null;
            }
            //获取最近的轨迹点距离目标点的距离差
            double distance = HaversineUtil.distanceTo(destPoint.getLatitude(), destPoint.getLongitude(), nearest.getLat(),nearest.getLon());
            log.info("destPoint.getLatitude:{},destPoint.getLongitude:{},nearest.getLatitude:{} nearest.getLongitude:{} distance:{}",destPoint.getLatitude(),destPoint.getLongitude(),Double.valueOf(nearest.getLat()),Double.valueOf(nearest.getLon()),distance);
            //当轨迹点信息为1时 此轨迹点即为最近轨迹点 组装返回值
            orderTraceBean.setLongitude(nearest.getLon());
            orderTraceBean.setLatitude(nearest.getLat());
            try {
                orderTraceBean.setUploadTime(sdf.parse(nearest.getGtm()));
            } catch (ParseException e) {
                log.error("【中交轨迹打点】异常：{}",e);
            }
            return orderTraceBean;
        } else if (orderPoints.size() > 1 && orderPoints.size() <= 180) {
            //情况二:当轨迹点数据量多个(大于1下于180)时
            //声明距离目标地最近轨迹点的信息
            CarLocation nearestTrace = new CarLocation();
            double minDistance = Double.MAX_VALUE;
            for (CarLocation point : orderPoints) {
                Double latitude = point.getLat();
                Double longitude = point.getLon();
                if(latitude != null&& longitude != null){
                    double distance = HaversineUtil.distanceTo(destPoint.getLatitude(), destPoint.getLongitude(), Double.valueOf(latitude), Double.valueOf(longitude));
                    log.info("orderPoints destPoint.getLatitude:{},destPoint.getLongitude:{},nearest.getLatitude:{} nearest.getLongitude:{} distance:{}",destPoint.getLatitude(),destPoint.getLongitude(),point.getLat(),point.getLon(),distance);
                    if (distance < minDistance) {
                        minDistance = distance;
                        nearestTrace = point;
                    }
                }
            }
            if(minDistance <  Double.MAX_VALUE){
                orderTraceBean.setLongitude(nearestTrace.getLon());
                orderTraceBean.setLatitude(nearestTrace.getLat());
                try {
                    orderTraceBean.setUploadTime(sdf.parse(nearestTrace.getGtm()));
                } catch (ParseException e) {
                    log.error("【中交轨迹打点】异常：{}",e);
                }
                return orderTraceBean;
            }
            return null;
        } else {
            //todo 待完善 情况三:当轨迹点数据量较多时 递归查询
            return findNearestRecursive(destPoint, orderPoints, 0);
        }
    }


    private static OrderTraceBean findNearestRecursive(DestPointBean destPoint, List<CarLocation> orderPoints, int depth) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

        //声明最近的订单轨迹点
        OrderTraceBean orderTraceBean = new OrderTraceBean();

        if (orderPoints.size() <= 1) {
            //当轨迹点信息为0时直接返回null
            if (orderPoints.isEmpty()) {
                return null;
            }
            //当轨迹点数量为1时 内容为空返回null
            CarLocation nearest = orderPoints.get(0);
            if (ObjectUtil.isEmpty(nearest)) {
                return null;
            }
            //获取最近的轨迹点距离目标点的距离差
            double distance = HaversineUtil.distanceTo(destPoint.getLatitude(), destPoint.getLongitude(),nearest.getLat(), nearest.getLon());
            //当轨迹点信息为1时 此轨迹点即为最近轨迹点 组装返回值
            orderTraceBean.setDistance(distance);
            orderTraceBean.setLongitude(nearest.getLon());
            orderTraceBean.setLatitude(nearest.getLat());
            try {
                orderTraceBean.setUploadTime(sdf.parse(nearest.getGtm()));
            } catch (ParseException e) {
                log.error("【中交轨迹打点】异常：{}",e);
            }
            return orderTraceBean;
        }

        //情况二:当轨迹点数据量多个(大于1)时 这里简单地将点集划分为两个区域作为示例
        List<List<CarLocation>> regions = new ArrayList<>();
        int halfSize = orderPoints.size() / 2;
        regions.add(orderPoints.subList(0, halfSize)); // 第一部分
        regions.add(orderPoints.subList(halfSize, orderPoints.size())); // 第二部分

        //声明距离目标地最近轨迹点的信息
        OrderTraceBean nearestBean = null;
        double minDistance = Double.MAX_VALUE;
        double distance = 0;
        for (List<CarLocation> region : regions) {
            OrderTraceBean nearestPoint = findNearestRecursive(destPoint, region, depth + 1);
            if (nearestPoint != null) {
                distance = HaversineUtil.distanceTo(destPoint.getLatitude(), destPoint.getLongitude(), Double.valueOf(nearestPoint.getLatitude()), Double.valueOf(nearestPoint.getLongitude()));
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestBean = nearestPoint;
                }
            }
        }
        nearestBean.setDistance(distance);
        // 如果划分深度足够，或者当前区域足够小，则直接返回最近点
        //深度计算方式 假如两个小时打满 有120个 每个区域有60个
        if (depth >= SOME_THRESHOLD || orderPoints.size() <= SOME_SMALL_SIZE) {
            return nearestBean;
        }
        return nearestBean;
    }
    private void addSendSms(TransportOrdersVO transportOrder,ReqParam reqParam){
        try {
            String smsAppealPass = tytMessageTmplService.getSmsTmpl("sms_appeal_pass");
            String smsAppealReject = tytMessageTmplService.getSmsTmpl("sms_appeal_reject");
            String smsAppealFalseOrder = tytMessageTmplService.getSmsTmpl("sms_appeal_false_order");
            log.info("模板==:{},模板1：{}",smsAppealPass,smsAppealReject);
            String[] valueArray = new String[2];
            String[] keyArray = new String[] { "${startPoint}", "${destPoint}" };
            // 发短信
            valueArray[0] = transportOrder.getStartPoint();
            valueArray[1] = transportOrder.getDestPoint();
            // 发短信提示用户
            String finalMessage = "";
            if("其他".equals(reqParam.getReason())){
                reqParam.setReason(reqParam.getRemark());
            }
            if(reqParam.getOperate()== OperateAppealEnum.PASS.getCode()){
                finalMessage = StringUtils.replaceEach(smsAppealPass, keyArray, valueArray);
            }else if(reqParam.getOperate()== OperateAppealEnum.REJECT.getCode()){
                finalMessage = StringUtils.replaceEach(smsAppealReject, keyArray, valueArray);
                insertReason(reqParam,2);
            }else if(reqParam.getOperate()== OperateAppealEnum.FALSE.getCode()){
                finalMessage = StringUtils.replaceEach(smsAppealFalseOrder, keyArray, valueArray);
                insertReason(reqParam,3);
            }else if(reqParam.getOperate()== OperateAppealEnum.WAIT_APPEAL.getCode()){
                insertReason(reqParam,4);
                return;
            }
            ShortMsgBean shortMsgBean = new ShortMsgBean();
            // 根据短信key获取短信模板
            shortMsgBean.setMessageType(MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
            String messageSerailNum = SerialNumUtil.generateSeriaNum();
            shortMsgBean.setMessageSerailNum(messageSerailNum);
            shortMsgBean.setContent(finalMessage);
            shortMsgBean.setCell_phone(transportOrder.getTel());
            shortMsgBean.setRemark("");
            tytMqMessageService.addMqMessage(messageSerailNum, JSON.toJSONString(shortMsgBean), MqBaseMessageBean.MESSAGETYPE_SEDN_MESSAGE);
            tytMqMessageService.sendMqMessage(messageSerailNum, JSON.toJSONString(shortMsgBean));
        } catch (Exception e) {
            log.error("【申诉单操作】出现异常：{}",e);
        }
    }

    public void saveUpdateRiskOrderStatus(TransportOrdersVO transportOrder,ReqParam reqParam) {
        log.info("【风控处理】入参：{},{}",transportOrder,reqParam);
        int operate = reqParam.getOperate();
       // 1.审核通过 2.驳回，补充凭证 3."轨迹符合，正常单 4.置为虚假单
        switch (operate){
            case 1:
                reqParam.setRiskControlStatus(4);
                break;
            case 2:
                reqParam.setRiskControlStatus(5);
                break;
            case 3:
                reqParam.setRiskControlStatus(1);
                break;
            case 4:
                reqParam.setRiskControlStatus(7);
                if(transportOrder.getInvoiceTransport() == 1 && transportOrder.getFreightStatus() > 0){
                    reqParam.setFreightStatus(30);
                }
                reqParam.setRefundReason("后台判定虚假单退款");
                reqParam.setOrderNewStatus(OrderStatusEnum.CANCEL.getCode()+"");
                reqParam.setRefundAmount(transportOrder.getPayAmount());
                reqParam.setCostStatus(35);
            default: break;
            case 5:
                reqParam.setRiskControlStatus(2);
               /* String reason = reqParam.getReason();
                if(StringUtils.isNotBlank(reason) && "其他".equals(reason)){
                    reqParam.setReason(reqParam.getRemark());
                }*/
        }
        log.info("【风控处理】更新状态参数：{}",reqParam);
        //更新tyt_transport_orders表
        transportOrdersMapper.updateById(reqParam);
        if(operate == 4){
            FreightOperateMessage freightOperateMessage = new FreightOperateMessage();
            freightOperateMessage.setMessageType(MqBaseMessageBean.ORDERS_FREIGHT_OPERATE_CODE);
            freightOperateMessage.setOrderId(transportOrder.getId());
            freightOperateMessage.setOperateType(FreightOperateMessage.OPERATE_TYPE_REFUND_FREIGHT);
            freightOperateMessage.setRefundReason("转虚假单退运费");
            freightOperateMessage.setMessageSerailNum(UUID.randomUUID() + "-manage");
            //生成mq消息
            MqInfoFeeOperateMsg operateMsg = new MqInfoFeeOperateMsg();
            operateMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
            operateMsg.setMessageType(MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
            operateMsg.setOpStatus(15);
            operateMsg.setAmount(String.valueOf(transportOrder.getPayAmount()));
            operateMsg.setCarOwnerUserId(transportOrder.getPayUserId());
            operateMsg.setShipperUserId(transportOrder.getUserId());
            operateMsg.setStartPoint(transportOrder.getStartPoint());
            operateMsg.setDestPoint(transportOrder.getDestPoint());
            operateMsg.setTaskContent(transportOrder.getTaskContent());
            operateMsg.setTsId(transportOrder.getTsId());
            operateMsg.setTsOrderNo(transportOrder.getTsOrderNo());
            operateMsg.setOrderId(transportOrder.getId());
            operateMsg.setInfoFeeServiceFee("0.00");
            operateMsg.setRefundType(1);
            operateMsg.setTecServiceFee(String.valueOf(transportOrder.getTecServiceFee()));
            operateMsg.setTechnicalServiceNo(transportOrder.getTechnicalServiceNo());
            //退运费
            if(transportOrder.getInvoiceTransport() == 1  && transportOrder.getFreightStatus() > 0){
                tytMqMessageService.addMqMessage(freightOperateMessage.getMessageSerailNum(),JSON.toJSONString(freightOperateMessage),MqBaseMessageBean.ORDERS_FREIGHT_OPERATE_CODE);
                tytMqMessageService.sendMqMessage(freightOperateMessage.getMessageSerailNum(), JSON.toJSONString(freightOperateMessage));
            }
            tytMqMessageService.addMqMessage(operateMsg.getMessageSerailNum(),JSON.toJSONString(operateMsg), MqBaseMessageBean.INFO_FEE_OPERATE_DEAL);
            tytMqMessageService.sendMqMessage(operateMsg.getMessageSerailNum(), JSON.toJSONString(operateMsg));
        }
    }
}
