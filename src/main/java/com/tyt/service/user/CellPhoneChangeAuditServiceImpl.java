package com.tyt.service.user;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.tyt.common.service.TytMessageTmplService;
import com.tyt.common.service.TytMqMessageService;
import com.tyt.manager.entity.base.AccountUpdateLog;
import com.tyt.manager.entity.base.TytCellPhoneChangeAudit;
import com.tyt.manager.entity.base.TytCellphone;
import com.tyt.manager.enums.CellPhoneChangeAuditStatusEnum;
import com.tyt.manager.mapper.base.AccountUpdateLogMapper;
import com.tyt.manager.mapper.base.TytCellPhoneChangeAuditMapper;
import com.tyt.manager.mapper.base.TytCellphoneMapper;
import com.tyt.manager.service.mq.MessageCenterPushService;
import com.tyt.service.cache.CacheService;
import com.tyt.util.Constant;
import com.tyt.util.PhoneFormatCheckUtils;
import com.tyt.util.UserTicketUtil;
import com.tyt.web.enums.CellPhoneChangeRejectReasonCodeEnum;
import com.tyt.web.enums.ChangeAuditSmsTempEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 更换手机号审核接口
 * @date 2023/09/06 13:05
 */
@Service
@Slf4j
public class CellPhoneChangeAuditServiceImpl implements CellPhoneChangeAuditService{
    @Autowired
    private TytCellPhoneChangeAuditMapper tytCellPhoneChangeAuditMapper;
    @Autowired
    private TytCellphoneMapper tytCellphoneMapper;
    @Autowired
    private AccountUpdateLogMapper accountUpdateLogMapper;
    @Autowired
    private TytMessageTmplService tytMessageTmplService;
    @Autowired
    private CacheService cacheService;
    @Autowired
    private MessageCenterPushService messageCenterPushService;

    @Override
    public List<TytCellPhoneChangeAudit> getList(Integer pageNum, Integer pageSize,String oldCellPhone,
                                                 String newCellPhone,
                                                 Long userId,
                                                 Integer auditStatus) {
        PageHelper.startPage(pageNum,pageSize);
        return tytCellPhoneChangeAuditMapper.selectList(oldCellPhone,newCellPhone,userId,auditStatus);
    }

    @Override
    public TytCellPhoneChangeAudit getById(Long id) {
        return tytCellPhoneChangeAuditMapper.selectByPrimaryKey(id);
    }

    @Transactional(value = "mybatisTransactionManager",rollbackFor = Exception.class)
    @Override
    public void audit(TytCellPhoneChangeAudit auditInfo) {
        log.info("变更手机号审核参数信息:{}", JSON.toJSONString(auditInfo));
        //更新审核信息
        auditInfo.setModifyTime(new Date());
        tytCellPhoneChangeAuditMapper.updateByPrimaryKeySelective(auditInfo);

        //审核成功修改相关手机号信息并记录变更记录
        if (Objects.equals(auditInfo.getAuditStatus(), CellPhoneChangeAuditStatusEnum.PASSED.getAuditStatus())) {
            updateCellPhone(auditInfo.getUserId(), auditInfo.getId(), auditInfo.getOldCellPhone().trim(), auditInfo.getNewCellPhone().trim(), auditInfo.getOptId(), auditInfo.getOptName());
        }

        //驳回
        String reasonMsgByCode = StringUtils.EMPTY;
        if (Objects.equals(auditInfo.getAuditStatus(), CellPhoneChangeAuditStatusEnum.REJECT.getAuditStatus())) {
            reasonMsgByCode = CellPhoneChangeRejectReasonCodeEnum.getReasonMsgByCode(auditInfo.getRejectReasonCode(), auditInfo.getRejectReasonMsg());
        }

        //发送短信
        //根据状态获取短信模版枚举信息
        ChangeAuditSmsTempEnum smsTempEnum = ChangeAuditSmsTempEnum.getByAuditStatus(auditInfo.getAuditStatus());
        if (Objects.nonNull(smsTempEnum)) {
            //短信内容
            String content = tytMessageTmplService.getSmsTmpl(smsTempEnum.getTempKey(), smsTempEnum.getDefaultContent());
            String smsContent = StringUtils.replaceEach(content, new String[]{"${content}"}, new String[]{reasonMsgByCode});
            if (StringUtils.isNotBlank(smsContent)) {
                log.info("变更手机号审核，发送短信内容为:{}", smsContent);
                messageCenterPushService.sendShortMsg(auditInfo.getNewCellPhone(),smsContent,"");
            }
        }
    }

    /**
     * @description 更改手机号相关数据
     * <AUTHOR>
     * @date 2023/9/6 18:08
     * @version 1.0
     * @param userId
     * @param auditId
     * @param oldPhone
     * @param newPhone
     * @return void
     */
    @Transactional(value = "mybatisTransactionManager", rollbackFor = Exception.class)
    public void updateCellPhone(Long userId, Long auditId,String oldPhone, String newPhone,Long opId,String opName) {
        tytCellphoneMapper.deleteFromTytCellphone(oldPhone);
        if (PhoneFormatCheckUtils.isMobile(newPhone)) {
            TytCellphone tytCellphone = tytCellphoneMapper.selectByCellPhone(newPhone);
            if (Objects.isNull(tytCellphone)) {
                tytCellphoneMapper.insertCellPhoneToTemp(newPhone);
            }
        }
        tytCellPhoneChangeAuditMapper.updateIdentityAuthMobile(userId, newPhone);
        tytCellPhoneChangeAuditMapper.updateBlacklistUser(userId, newPhone, oldPhone);
        tytCellPhoneChangeAuditMapper.updateUserCellphoneInfo(userId, newPhone, "0000");
        AccountUpdateLog accountUpdateLog = new AccountUpdateLog();
        accountUpdateLog.setCellPhone(oldPhone);
        accountUpdateLog.setNewPhone(newPhone);
//        accountUpdateLog.setReason("");
        accountUpdateLog.setUserId(userId);
        accountUpdateLog.setUpdateType(2);
        accountUpdateLog.setOptId(opId);
        accountUpdateLog.setOptName(opName);
        accountUpdateLog.setCtime(new Date());
        accountUpdateLog.setCellPhoneChangeEditId(auditId);
        accountUpdateLogMapper.insertSelective(accountUpdateLog);
        // 删除缓存 提出登录状态
        UserTicketUtil.kickOutAllClient(userId.toString());
        cacheService.del(Constant.CACHE_USER_KEY + userId);
    }
}
