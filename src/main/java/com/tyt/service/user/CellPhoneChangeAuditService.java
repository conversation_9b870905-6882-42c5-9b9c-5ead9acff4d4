package com.tyt.service.user;

import com.tyt.manager.entity.base.TytCellPhoneChangeAudit;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 更换手机号审核接口
 * @date 2023/09/06 13:05
 */
public interface CellPhoneChangeAuditService {
    /**
     * @description 分页获取列表数据
     * <AUTHOR>
     * @date 2023/9/6 13:08
     * @version 1.0
     * @param pageNum
     * @param pageSize
     * @return java.util.List<com.tyt.manager.entity.base.TytCellPhoneChangeAudit>
     */
    List<TytCellPhoneChangeAudit> getList(Integer pageNum,Integer pageSize, String oldCellPhone,
                                           String newCellPhone,
                                          Long userId,
                                           Integer auditStatus);

    /**
     * @description 根据id获取审核信息
     * <AUTHOR>
     * @date 2023/9/6 13:54
     * @version 1.0
     * @param id
     * @return com.tyt.manager.entity.base.TytCellPhoneChangeAudit
     */
    TytCellPhoneChangeAudit getById(Long id);

    /**
     * @description 审核
     * @param auditInfo
     * @return void
     * <AUTHOR>
     * @date 2023/9/6 15:58
     * @version 1.0
     */
    void audit(TytCellPhoneChangeAudit auditInfo);
}
