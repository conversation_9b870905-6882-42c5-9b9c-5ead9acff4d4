package com.tyt.techRefund.controller;


import com.alibaba.fastjson.JSON;
import com.tyt.AbstracTest;
import com.tyt.service.common.redis.RedisUtil;
import com.tyt.techRefund.bean.BatchTecAuditReq;
import com.tyt.techRefund.service.TechRefundService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 完单技术服务费退还财务审核单元测试类
 *
 * <AUTHOR>
 * @since 2024/7/2 16:11
 */
@Slf4j
public class TechRefundFinanceControllerTest extends AbstracTest {


    @Autowired
    private TechRefundService techRefundService;

    @Autowired
    private TechRefundFinanceController controller;

    /**
     * 完单技术服务费退还财务审核方法
     *
     * <AUTHOR>
     * @param
     * @return void
     */
    @Test
    public void testUpdateFinanceAuditStatus() throws Exception {

        mockMvc.perform(MockMvcRequestBuilders.get("/tech/refund/finance/audit")
                .param("id", "11")
                .param("auditStatus", "1"))
                .andDo((it) -> log.info(new String(it.getResponse().getContentAsByteArray(), StandardCharsets.UTF_8))).andExpect(status().isOk());

    }

    @Test
    public void testBatchTecAudit() throws Exception {
        BatchTecAuditReq req = new BatchTecAuditReq();
        req.setIds("1,2,3");
        req.setAuditStatus(1);

        mockMvc.perform(MockMvcRequestBuilders.post("/tech/refund/finance/batchAudit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JSON.toJSONString(req)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    // 测试批量审核结果查询
    @Test
    public void testListBatchTecRefund() throws Exception {
        String ids = "1,2,3";

        mockMvc.perform(MockMvcRequestBuilders.post("/tech/refund/finance/listBatchTecRefund")
                        .param("ids", ids))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.successIds").exists());
    }

    // 测试失败结果导出
    @Test
    public void testExportFailTecRefundToExcel() throws Exception {
        String ids = "1,2";
        mockMvc.perform(MockMvcRequestBuilders.get("/tech/refund/finance/exportFailTecRefundToExcel")
                        .param("ids", ids))
                .andExpect(status().isOk())
                .andExpect(content().contentType("application/vnd.ms-excel"));
    }


}
